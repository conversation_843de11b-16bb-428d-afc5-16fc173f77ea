/**
 * Cron Jobs Index
 *
 * This file serves as the main entry point for all cron jobs in the application.
 * It imports and initializes all scheduled tasks.
 */

const { logCronExecution, CRON_PATTERNS } = require('../Configs/cron-job');

// Import cron job modules
const userDeletionCron = require('./cron');

// Log cron jobs initialization
logCronExecution('SYSTEM', 'START', 'Initializing cron jobs');

// List of all active cron jobs for monitoring
const activeCronJobs = [
  {
    name: 'User Button Data Deletion Job',
    description:
      'Deletes user button data (buttons, items, alarms) based on subscription expiration while keeping user and subscription records intact',
    schedule: CRON_PATTERNS.EVERY_5_MINUTES,
    timezone: 'UTC',
    module: userDeletionCron,
    status: 'active',
  },
];

// Log active cron jobs
console.log('\n=== ACTIVE CRON JOBS ===');
activeCronJobs.forEach(job => {
  console.log(`📅 ${job.name}`);
  console.log(`   Description: ${job.description}`);
  console.log(`   Schedule: ${job.schedule} (${job.timezone})`);
  console.log(`   Status: ${job.status}`);
  console.log('');
});

logCronExecution('SYSTEM', 'SUCCESS', `Initialized ${activeCronJobs.length} cron job(s)`);

// Export for external access and testing
module.exports = {
  userDeletionCron,
  activeCronJobs,

  // Legacy export for backward compatibility
  subscriptionCron: userDeletionCron
};