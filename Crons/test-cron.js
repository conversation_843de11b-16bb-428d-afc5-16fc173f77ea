/**
 * Cron Job Test Suite
 *
 * This file contains test functions to validate the cron job functionality
 * without affecting production data.
 */

// Import cron functions for testing (not directly used but available for manual testing)
// const { runUserButtonDataDeletionJob, deleteUserButtonData } = require('./cron');
const { validateCronExpression, CRON_PATTERNS } = require('../Configs/cron-job');
const moment = require('moment');

/**
 * Test cron expression validation
 */
const testCronValidation = () => {
  console.log('\n=== Testing Cron Expression Validation ===');

  const testCases = [
    { expression: '0 2 * * *', expected: true, description: 'Daily at 2 AM' },
    { expression: '*/5 * * * *', expected: true, description: 'Every 5 minutes' },
    { expression: '0 0 1 * *', expected: true, description: 'Monthly on 1st' },
    { expression: '60 * * * *', expected: false, description: 'Invalid minute (60)' },
    { expression: '* 25 * * *', expected: false, description: 'Invalid hour (25)' },
    { expression: '* * * * * *', expected: false, description: 'Too many fields' },
    { expression: '* * *', expected: false, description: 'Too few fields' }
  ];

  testCases.forEach(test => {
    const result = validateCronExpression(test.expression);
    const status = result === test.expected ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${test.description}: "${test.expression}" -> ${result}`);
  });
};

/**
 * Test cron patterns from configuration
 */
const testCronPatterns = () => {
  console.log('\n=== Testing Predefined Cron Patterns ===');

  Object.entries(CRON_PATTERNS).forEach(([name, pattern]) => {
    const isValid = validateCronExpression(pattern);
    const status = isValid ? '✅ VALID' : '❌ INVALID';
    console.log(`${status} ${name}: "${pattern}"`);
  });
};

/**
 * Test database connection and schema access
 */
const testDatabaseConnection = async () => {
  console.log('\n=== Testing Database Connection ===');

  try {
    const { UserSubscription: UserSubscriptionSchema } = require('../Database/Schemas');

    // Test basic query without affecting data
    const count = await UserSubscriptionSchema.count();
    console.log('✅ Database connection successful');
    console.log(`📊 Total user subscriptions: ${count}`);

    // Test query for expired subscriptions (without deleting)
    const currentTime = moment.utc().toDate();
    const expiredCount = await UserSubscriptionSchema.count({
      where: {
        deletion_date: {
          [require('sequelize').Op.lte]: currentTime,
        },
      },
    });

    console.log(`⏰ Expired subscriptions found: ${expiredCount}`);

    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
};

/**
 * Test the user button data deletion job in dry-run mode
 */
const testUserButtonDataDeletionDryRun = async () => {
  console.log('\n=== Testing User Button Data Deletion Job (Dry Run) ===');

  try {
    const { UserSubscription: UserSubscriptionSchema, Button: ButtonSchema, Item: ItemSchema, Alarm: AlarmSchema } = require('../Database/Schemas');
    const { Op } = require('sequelize');

    // Find users that would have their button data deleted (without actually deleting)
    const currentTime = moment.utc().toDate();
    const expiredSubs = await UserSubscriptionSchema.findAll({
      attributes: ['user_id', 'deletion_date'],
      where: {
        deletion_date: {
          [Op.lte]: currentTime,
        },
      },
      raw: true
    });

    const userIdsToProcess = [...new Set(expiredSubs.map((sub) => sub.user_id))];

    console.log(`🔍 Found ${userIdsToProcess.length} users that would have their button data deleted`);

    if (userIdsToProcess.length > 0) {
      console.log('👥 User IDs:', userIdsToProcess);

      // Show deletion dates and data counts for context
      for (const sub of expiredSubs) {
        const deletionDate = moment(sub.deletion_date).format('YYYY-MM-DD HH:mm:ss');

        // Get counts of data that would be deleted
        const buttonCount = await ButtonSchema.count({ where: { user_id: sub.user_id } });
        const itemCount = await ItemSchema.count({ where: { user_id: sub.user_id } });
        const alarmCount = await AlarmSchema.count({ where: { user_id: sub.user_id } });

        console.log(`   User ${sub.user_id}: deletion_date = ${deletionDate}`);
        console.log(`     - Buttons: ${buttonCount}, Items: ${itemCount}, Alarms: ${alarmCount}`);
      }
    }

    return { userCount: userIdsToProcess.length, userIds: userIdsToProcess };
  } catch (error) {
    console.error('❌ Dry run test failed:', error.message);
    return null;
  }
};

/**
 * Test timezone handling
 */
const testTimezoneHandling = () => {
  console.log('\n=== Testing Timezone Handling ===');

  const now = new Date();
  const utcTime = moment.utc().toDate();
  const localTime = moment().toDate();

  console.log(`🌍 Current local time: ${localTime.toISOString()}`);
  console.log(`🌐 Current UTC time: ${utcTime.toISOString()}`);
  console.log(`⏰ System timezone offset: ${now.getTimezoneOffset()} minutes`);

  // Test if UTC time is being used correctly
  const timeDiff = Math.abs(utcTime.getTime() - now.getTime());
  if (timeDiff < 1000) { // Less than 1 second difference
    console.log('✅ UTC time handling appears correct');
  } else {
    console.log('⚠️  Potential timezone issue detected');
  }
};

/**
 * Run all tests
 */
const runAllTests = async () => {
  console.log('🧪 Starting Cron Job Test Suite');
  console.log('=====================================');

  // Run synchronous tests
  testCronValidation();
  testCronPatterns();
  testTimezoneHandling();

  // Run asynchronous tests
  const dbConnected = await testDatabaseConnection();

  if (dbConnected) {
    await testUserButtonDataDeletionDryRun();
  } else {
    console.log('⚠️  Skipping button data deletion tests due to database connection issues');
  }

  console.log('\n🏁 Test Suite Completed');
  console.log('=====================================');
};

// Export test functions
module.exports = {
  testCronValidation,
  testCronPatterns,
  testDatabaseConnection,
  testUserButtonDataDeletionDryRun,
  testTimezoneHandling,
  runAllTests,

  // Legacy export for backward compatibility
  testUserDeletionDryRun: testUserButtonDataDeletionDryRun
};

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}
