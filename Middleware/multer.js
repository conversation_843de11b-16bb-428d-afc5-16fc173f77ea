/*
 * Summary:     Multer is middleware for upload image
 * Author:      Openxcell(empCode-N00039)
 */
const multer = require("multer");
const path = require("path");
const os = require("os");
const fs = require("fs");
const tmpdir = os.tmpdir();
var storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, tmpdir);
  },
  filename: function (req, file, cb) {
    const imageName = Date.now() + path.extname(file.originalname);
    const filepath = path.join(tmpdir, imageName);
    file.originalname = imageName;
    fs.mkdtemp(filepath, (err, folder) => {
      if (err) throw err;
      cb(null, imageName);
    });
  },
});

const fileFilter = (req, file, cb) => { 
  // Accept only specific file types
  if (file.mimetype === 'image/jpeg' || file.mimetype === 'image/png' || file.mimetype === 'image/jpg' || file.mimetype === 'application/octet-stream' ) {
    cb(null, true);
  } else {
    cb(new Error('Please upload a valid image file (e.g., JPG, PNG,JPEG)'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
});

exports.singleFileUpload = (req, res, next) => {
    upload.single('profilePicture')(req, res, (err) => {
      next();
    });
};

exports.multiFileUpload = multer({
  storage: storage,
}).any();
