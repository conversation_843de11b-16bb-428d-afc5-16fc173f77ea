const { body, query } = require('express-validator');
const { headerValidator } = require('../CommonValidator');

exports.createTag = [
  ...headerValidator,
  body('buttonId')
    .trim()
    .notEmpty()
    .withMessage('Button ID is required')
    .isInt()
    .withMessage('Button ID must be an integer'),
  body('itemId')
    .trim()
    .notEmpty()
    .withMessage('Item ID is required')
    .isInt()
    .withMessage('Item ID must be an integer'),

  body('tagUuid')
    .trim()
    .notEmpty()
    .withMessage('Tag UUID is required.')
    .isString()
    .withMessage('Tag UUID must be a string.'),

  body('tagType')
    .trim()
    .notEmpty()
    .withMessage('Tag type is required')
    .isIn(['NOTE', 'GPS'])
    .withMessage('Tag type must be NOTE, or GPS'),
  body('tagTitle').trim().notEmpty().withMessage('Tag title is required'),
  body('tagValue').trim().notEmpty().withMessage('Tag value is required'),
  body('tagTimeStamp')
    .trim()
    .notEmpty()
    .withMessage('Tag time stamp is required'),
];

exports.updateTag = [
  ...headerValidator,
  body('tagId')
    .trim()
    .notEmpty()
    .withMessage('Tag ID is required')
    .isInt()
    .withMessage('Tag ID must be an integer'),
  body('buttonId')
    .trim()
    .notEmpty()
    .withMessage('Button ID is required')
    .isInt()
    .withMessage('Button ID must be an integer'),
  body('itemId')
    .trim()
    .notEmpty()
    .withMessage('Item ID is required')
    .isInt()
    .withMessage('Item ID must be an integer'),
  body('tagUuid')
    .trim()
    .notEmpty()
    .withMessage('Tag UUID is required.')
    .isString()
    .withMessage('Tag UUID must be a string.'),
  body('tagType')
    .trim()
    .notEmpty()
    .withMessage('Tag type is required')
    .isIn(['NOTE', 'GPS'])
    .withMessage('Tag type must be NOTE, or GPS'),
  body('tagTitle').trim().notEmpty().withMessage('Tag title is required'),
  body('tagValue').trim().notEmpty().withMessage('Tag value is required'),
  body('tagTimeStamp')
    .trim()
    .notEmpty()
    .withMessage('Tag time stamp is required'),
];

exports.deleteTag = [
  ...headerValidator,
  query('tagId')
    .trim()
    .notEmpty()
    .withMessage('Tag ID is required')
    .isInt()
    .withMessage('Tag ID must be an integer'),
  query('itemId')
    .trim()
    .notEmpty()
    .withMessage('Item ID is required')
    .isInt()
    .withMessage('Item ID must be an integer'),
  query('buttonId')
    .trim()
    .notEmpty()
    .withMessage('Button ID is required')
    .isInt()
    .withMessage('Button ID must be an integer'),
];
