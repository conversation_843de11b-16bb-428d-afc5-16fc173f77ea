const { body } = require('express-validator');
const { headerValidator } = require('../CommonValidator');

exports.updateAlarm = [
  ...headerValidator,
  body('alarmId')
    .trim()
    .notEmpty()
    .withMessage('Alarm ID is required')
    .isInt()
    .withMessage('Alarm ID must be an integer'),
  body('buttonId')
    .trim()
    .notEmpty()
    .withMessage('Button ID is required')
    .isInt()
    .withMessage('Button ID must be an integer'),
  body('alarmTime').trim().notEmpty().withMessage('Alarm time is required'),
  body('snoozeTime').trim().optional(),
  body('repeatDaily').trim().notEmpty().withMessage('Repeat daily is required'),
  body('isRingAfter')
    .trim()
    .notEmpty()
    .withMessage('Is ring after is required'),
  body('isRingAfterAlways')
    .trim()
    .notEmpty()
    .withMessage('Is ring after always is required'),
  body('ringAfterTimeStamp')
    .trim()
    .if(body('isRingAfter').equals('true'))
    .notEmpty()
    .withMessage('Ring after time stamp is required'),
  body('ringAfterTimeMs')
    .trim()
    .if(body('isRingAfter').equals('true'))
    .notEmpty()
    .withMessage('Ring after time stamp is required'),
  body('isActive').trim().notEmpty().withMessage('Is active is required'),
];
