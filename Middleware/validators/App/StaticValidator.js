const { body, query } = require('express-validator');
const { headerValidator } = require('../CommonValidator');
const { STATIC_PAGE_TYPES } = require('../../../Configs/constants');

exports.fetchStatic = [
  // ...headerValidator,
  query('staticContentType')
    .trim()
    .notEmpty()
    .withMessage('staticContentType is required')
    .isIn(Object.values(STATIC_PAGE_TYPES))
    .withMessage(
      'Invalid type. It should be one of ' +
        Object.values(STATIC_PAGE_TYPES).join(', ')
    ),
];
