const { body } = require('express-validator');
const { headerValidator } = require('../CommonValidator');

exports.batchSyncValidator = [
  ...headerValidator,
  body('operations')
    .isArray({ min: 1 })
    .withMessage('operations must be a non-empty array'),

  body('operations.*.operationType')
    .isString()
    .notEmpty()
    .withMessage('operationType must be a non-empty string'),

  body('operations.*.payload')
    .isObject()
    .withMessage('payload must be an object'),

  body('operations.*.syncId')
    .isInt()
    .notEmpty()
    .withMessage('syncId must be a non-empty integer'),

  body('operations.*.localButtonId')
    .isInt()
    .withMessage('buttonId must be an integer'),

  body('operations.*.localItemId')
    .optional()
    .isInt()
    .withMessage('itemId must be an integer'),

  body('operations.*.localTagId')
    .optional()
    .isInt()
    .withMessage('tagId must be an integer'),

  body('operations.*.buttonUuid')
    .trim()
    .optional()
    .isString()
    .withMessage('buttonUuid must be a string'),

  body('operations.*.itemUuid')
    .trim()
    .optional()
    .isString()
    .withMessage('itemUuid must be a string'),

  body('operations.*.tagUuid')
    .trim()
    .optional()
    .isString()
    .withMessage('tagUuid must be a string'),
];
