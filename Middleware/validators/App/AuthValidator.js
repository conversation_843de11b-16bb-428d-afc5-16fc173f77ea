const { header, body, query } = require('express-validator');

const {
  DEVICE_TYPE,
  USER_TYPE,
  OTP_TYPES,
} = require('../../../Configs/constants');
const { headerValidator } = require('../CommonValidator');

exports.signUp = [
  body('name', 'Please Provide valid Name!').trim().notEmpty(),
  body('email', 'Please Provide valid Email Id!').trim().isEmail(),
  body('phoneNumber', 'Please Provide valid Phone Number!').trim().notEmpty(),
  body('countryCode', 'Please Provide valid countryCode!').trim().notEmpty(),
  body('password', 'Please Provide password.')
    .trim()
    .notEmpty()
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[@#$%^&+=]).{8,}$/, 'i')
    .withMessage(
      'Password should be a minimum of 8 characters and must include one lowercase character, one uppercase character, a number, and a special character.'
    ),
  body('userType', 'Please Provide userType!')
    .isIn(Object.values(USER_TYPE))
    .withMessage(
      `userType value should be in ${Object.values(USER_TYPE).join(', ')}`
    ),
  body('deviceToken', 'Please Provide valid deviceToken!').trim().notEmpty(),
  body('appVersion', 'appVersion field is required!').trim().notEmpty(),
  body('deviceType', 'deviceType field is required!')
    .isIn(Object.values(DEVICE_TYPE))
    .withMessage(
      `deviceType value should be in ${Object.values(DEVICE_TYPE).join(', ')}`
    ),
];

exports.verifyOtp = [
  body('userId', 'userId field is required!').trim().notEmpty(),
  body('otp', 'otp field is required!')
    .trim()
    .notEmpty()
    .isInt({ min: 1000, max: 9999 })
    .withMessage('Please Provide valid otp!'),
  body('email', 'Please Provide valid email!').trim().isEmail(),
  body('countryCode', 'Please Provide valid countryCode!').trim().notEmpty(),
  body('phoneNumber', 'phoneNumber field is required!').trim().notEmpty(),
  body('otpType', 'otpType field is required!')
    .trim()
    .notEmpty()
    .isIn(Object.values(OTP_TYPES)),
];

exports.resendOtp = [
  body('userId', 'userId field is required!').trim().notEmpty(),
  body('email', 'Please Provide valid email!').trim().isEmail(),
  body('countryCode', 'Please Provide valid countryCode!').trim().notEmpty(),
  body('phoneNumber', 'phoneNumber field is required!').trim().notEmpty(),
  body('otpType', 'otpType field is required!')
    .trim()
    .notEmpty()
    .isIn(Object.values(OTP_TYPES)),
];

exports.checkStatus = [
  body('email', 'Please Provide valid Email Id!').trim().notEmpty().isEmail(),
  body('googleId', 'Please Provide valid googleId!')
    .trim()
    .notEmpty()
    .isString(),
];

exports.signIn = [
  body('userType', 'Please Provide userType.')
    .isIn(Object.values(USER_TYPE))
    .withMessage(
      `userType value should be in ${Object.values(USER_TYPE).join(', ')}`
    ),
  body('email')
    .if(body('userType').equals(USER_TYPE.NORMAL))
    .optional({ checkFalsy: true })
    .isEmail()
    .withMessage('Please provide a valid email address.'),
  body('countryCode')
    .if(body('userType').equals(USER_TYPE.NORMAL))
    .trim()
    // .notEmpty()
    .custom((value, { req }) => {
      const hasPhoneNumber = req.body.phoneNumber;

      if (hasPhoneNumber && !value) {
        throw new Error('Please provide country code with phone number.');
      }

      if (!hasPhoneNumber && value) {
        throw new Error('COuntry code is only required with phone number.');
      }

      return true;
    }),
  body('phoneNumber')
    .if(body('userType').equals(USER_TYPE.NORMAL))
    .optional({ checkFalsy: true })
    .isMobilePhone()
    .matches(/^(?=.*[0-9])/)
    .withMessage('Please provide a valid phone number.'),
  body('password', 'Please Provide password.')
    .if(body('userType').equals(USER_TYPE.NORMAL))
    .trim()
    .notEmpty(),
  body('name', 'Please Provide valid Name!')
    .if(body('userType').equals(USER_TYPE.GOOGLE))
    .trim()
    .notEmpty(),
  body('email', 'Please Provide valid email!')
    .if(body('userType').equals(USER_TYPE.GOOGLE))
    .trim()
    .notEmpty()
    .isEmail(),
  body('googleId', 'Please Provide valid googleId!')
    .if(body('userType').equals(USER_TYPE.GOOGLE))
    .trim()
    .notEmpty(),
  // body('email', 'Please Provide valid email!')
  //   .if(body('userType').equals(USER_TYPE.APPLE))
  //   .trim()
  //   .notEmpty()
  //   .isEmail(),
  // body('appleId', 'Please Provide valid appleId!')
  //   .if(body('userType').equals(USER_TYPE.APPLE))
  //   .trim()
  //   .notEmpty(),
  body('deviceType', 'deviceType field is required!')
    .isIn(Object.values(DEVICE_TYPE))
    .withMessage(
      `deviceType value should be in ${Object.values(DEVICE_TYPE).join(', ')}`
    ),
  body('deviceToken', 'Please Provide valid deviceToken!').trim().notEmpty(),
  body('appVersion', 'Please Provide valid appVersion!').trim().notEmpty(),
  body()
    .if(body('userType').equals(USER_TYPE.NORMAL))
    .custom((value, { req }) => {
      const hasEmail = req.body.email;
      const hasPhoneNumber = req.body.phoneNumber;

      if (hasEmail && hasPhoneNumber) {
        throw new Error(
          'Please provide either an email or a phone number, but not both.'
        );
      }
      if (!hasEmail && !hasPhoneNumber) {
        throw new Error('Please provide either an email or a phone number.');
      }
      return true;
    }),
];

exports.forgotPassword = [
  // body('userId').trim().notEmpty().withMessage('Please provide user id.'),

  body('email')
    .optional({ checkFalsy: true })
    .isEmail()
    .withMessage('Please provide a valid email address.'),

  body('countryCode')
    .optional({ checkFalsy: true })
    .trim()
    .notEmpty()
    .custom((value, { req }) => {
      const hasPhoneNumber = req.body.phoneNumber;

      if (hasPhoneNumber && !value) {
        throw new Error('Please provide country code with phone number.');
      }

      if (!hasPhoneNumber && value) {
        throw new Error('COuntry code is only required with phone number.');
      }
      return true;
    }),

  body('phoneNumber')
    .optional({ checkFalsy: true })
    .isMobilePhone()
    .matches(/^(?=.*[0-9])/)
    .withMessage('Please provide a valid phone number.'),

  body().custom((value, { req }) => {
    const hasEmail = req.body.email;
    const hasPhoneNumber = req.body.phoneNumber;

    if (hasEmail && hasPhoneNumber) {
      throw new Error(
        'Please provide either an email or a phone number, but not both.'
      );
    }
    if (!hasEmail && !hasPhoneNumber) {
      throw new Error('Please provide either an email or a phone number.');
    }
    return true;
  }),
];

exports.resetPassword = [
  // body('userId', 'Please Provide user id.').trim().notEmpty(),
  body('email')
    .optional({ checkFalsy: true })
    .isEmail()
    .withMessage('Please provide a valid email address.'),

  body('countryCode').optional({ checkFalsy: true }).trim().notEmpty(),

  body('phoneNumber')
    .optional({ checkFalsy: true })
    .isMobilePhone()
    .matches(/^(?=.*[0-9])/)
    .withMessage('Please provide a valid phone number.'),

  body('otp', 'Please Provide otp.').trim().notEmpty(),

  body('newPassword', 'Please provide new password.')
    .trim()
    .notEmpty()
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[@#$%^&+=]).{8,}$/, 'i')
    .withMessage(
      'Password should be a minimum of 8 characters and must include one lowercase character, one uppercase character, a number, and a special character.'
    ),

  body('confirmPassword', 'Please provide confirmation password.')
    .trim()
    .notEmpty(),

  body().custom((value, { req }) => {
    const newPassword = req.body.newPassword;
    const confirmPassword = req.body.confirmPassword;

    if (newPassword !== confirmPassword) {
      throw new Error('Passwords do not match.');
    }

    return true;
  }),

  body().custom((value, { req }) => {
    const hasEmail = req.body.email;
    const hasPhoneNumber = req.body.phoneNumber;

    if (hasEmail && hasPhoneNumber) {
      throw new Error(
        'Please provide either an email or a phone number, but not both.'
      );
    }
    if (!hasEmail && !hasPhoneNumber) {
      throw new Error('Please provide either an email or a phone number.');
    }
    return true;
  }),
];

exports.signOut = [...headerValidator];

// exports.notification = [
//   body('status', 'Please Provide notification status!').trim().notEmpty(),
// ];
