const { body, query } = require('express-validator');
const { headerValidator } = require('../CommonValidator');
const { BUTTON_SHAPES } = require('../../../Configs/constants');

exports.createButton = [
  ...headerValidator,

  body('buttonName')
    .trim()
    .notEmpty()
    .withMessage('Button name is required')
    .isString()
    .withMessage('Button name must be a string'),

  body('buttonUuid')
    .trim()
    .notEmpty()
    .withMessage('Button UUID is required.')
    .isString()
    .withMessage('Button UUID must be a string.'),

  body('buttonColor')
    .trim()
    .notEmpty()
    .withMessage('Button color is required')
    .isString()
    .withMessage('Button color must be a string')
    .isHexColor()
    .withMessage('Button color must be a valid hex color')
    .custom((value) => {
      if (!value.startsWith('#')) {
        throw new Error('Button color must start with #');
      }
      if (value.length !== 7) {
        throw new Error('Button color must be a valid hex color');
      }
      return true;
    }),

  body('buttonType')
    .trim()
    .notEmpty()
    .withMessage('Button type is required')
    .isInt({ min: 1, max: 3 })
    .withMessage('Button type must be 1, 2, or 3')
    .customSanitizer((value) => {
      // Convert numeric type to string enum
      const typeMap = {
        1: 'COUNT',
        2: 'DURATION',
        3: 'VALUE',
      };
      return typeMap[value];
    })
    .custom((value) => {
      if (!value) {
        throw new Error('Invalid button type');
      }
      return true;
    }),

  body('buttonShape')
    .trim()
    .notEmpty()
    .withMessage('Button shape is required')
    .isString()
    .withMessage('Button shape must be a string')
    .isIn(Object.values(BUTTON_SHAPES))
    .withMessage(
      `Button shape must be one of ${Object.values(BUTTON_SHAPES).join(', ')}`
    ),

  body('countInc', 'Count increment is required')
    .trim()
    .custom((value, { req }) => {
      if (req.body.buttonType === 'COUNT') {
        if (!value) {
          throw new Error(
            'Count increment is required for `COUNT` button type'
          );
        }
        if (!Number.isInteger(Number(value))) {
          throw new Error('Count increment must be an integer');
        }
      } else if (value) {
        throw new Error(
          'Count increment should only be present for `COUNT` button type'
        );
      }
      return true;
    }),

  body('valueUnit')
    .trim()
    .custom((value, { req }) => {
      if (req.body.buttonType === 'VALUE') {
        // if (!value) {
        //   throw new Error('Value unit is required for `VALUE` button type');
        // }
        if (typeof value !== 'string') {
          throw new Error('Value unit must be a string');
        }
      } else if (value) {
        throw new Error(
          'Value unit should only be present for `VALUE` button type'
        );
      }
      return true;
    }),

  body('valueUnitDescription')
    .trim()
    .custom((value, { req }) => {
      if (req.body.buttonType === 'VALUE') {
        // if (!value) {
        //   throw new Error(
        //     'Value unit description is required for `VALUE` button type'
        //   );
        // }
        if (typeof value !== 'string') {
          throw new Error('Value unit description must be a string');
        }
      } else if (value) {
        throw new Error(
          'Value unit description should only be present for `VALUE` button type'
        );
      }
      return true;
    }),

  body('valueItemName')
    .trim()
    .custom((value, { req }) => {
      if (req.body.buttonType === 'VALUE') {
        if (value === undefined) {
          throw new Error(
            'Value item name is required for `VALUE` button type'
          );
        }
        if (typeof value !== 'string') {
          throw new Error('Value item name must be a string');
        }
      } else if (value) {
        throw new Error(
          'Value item name should only be present for `VALUE` button type'
        );
      }
      return true;
    }),

  body('alarmTag', 'Alarm tag is required')
    .trim()
    .isBoolean()
    .withMessage('Alarm tag must be a boolean'),

  body('textNoteTag', 'Text note tag is required')
    .trim()
    .isBoolean()
    .withMessage('Text note tag must be a boolean'),

  body('locationTag', 'Location tag is required')
    .trim()
    .isBoolean()
    .withMessage('Location tag must be a boolean'),

  body('buttonSummeryCalculation', 'Button summary calculation is required')
    .trim()
    .notEmpty()
    .withMessage('Button summary calculation is required')
    .custom((value) => {
      if (!Array.isArray(value)) {
        throw new Error('Button summary calculation must be an array');
      }
      if (!value.every((item) => typeof item === 'string')) {
        throw new Error(
          'All items in button summary calculation must be strings'
        );
      }
      return true;
    }),

  body('buttonSequence', 'Button sequence is required')
    .trim()
    .notEmpty()
    .withMessage('Button sequence is required')
    .isInt()
    .withMessage('Button sequence must be an integer'),
];

exports.fetchButton = [
  ...headerValidator,
  query('buttonId')
    .trim()
    .notEmpty()
    .withMessage('Button id is required')
    .isInt()
    .withMessage('Button id must be an integer'),
];

exports.updateButton = [
  ...headerValidator,
  body('buttonId')
    .trim()
    .notEmpty()
    .withMessage('Button id is required')
    .isInt()
    .withMessage('Button id must be an integer'),

  body('buttonUuid')
    .trim()
    .notEmpty()
    .withMessage('Button UUID is required.')
    .isString()
    .withMessage('Button UUID must be a string.'),

  body('buttonName')
    .trim()
    .notEmpty()
    .withMessage('Button name is required')
    .isString()
    .withMessage('Button name must be a string'),

  body('buttonColor')
    .trim()
    .notEmpty()
    .withMessage('Button color is required')
    .isString()
    .withMessage('Button color must be a string')
    .isHexColor()
    .withMessage('Button color must be a valid hex color')
    .custom((value) => {
      if (!value.startsWith('#')) {
        throw new Error('Button color must start with #');
      }
      if (value.length !== 7) {
        throw new Error('Button color must be a valid hex color');
      }
      return true;
    }),

  body('buttonType')
    .trim()
    .notEmpty()
    .withMessage('Button type is required')
    .isInt({ min: 1, max: 3 })
    .withMessage('Button type must be 1, 2, or 3')
    .customSanitizer((value) => {
      // Convert numeric type to string enum
      const typeMap = {
        1: 'COUNT',
        2: 'DURATION',
        3: 'VALUE',
      };
      return typeMap[value];
    })
    .custom((value) => {
      if (!value) {
        throw new Error('Invalid button type');
      }
      return true;
    }),

  body('buttonShape')
    .trim()
    .notEmpty()
    .withMessage('Button shape is required')
    .isString()
    .withMessage('Button shape must be a string')
    .isIn(Object.values(BUTTON_SHAPES))
    .withMessage(
      `Button shape must be one of ${Object.values(BUTTON_SHAPES).join(', ')}`
    ),

  body('countInc', 'Count increment is required')
    .trim()
    .custom((value, { req }) => {
      if (req.body.buttonType === 'COUNT') {
        if (!value) {
          throw new Error(
            'Count increment is required for `COUNT` button type'
          );
        }
        if (!Number.isInteger(Number(value))) {
          throw new Error('Count increment must be an integer');
        }
      } else if (value) {
        throw new Error(
          'Count increment should only be present for `COUNT` button type'
        );
      }
      return true;
    }),

  body('valueUnit')
    .trim()
    .custom((value, { req }) => {
      if (req.body.buttonType === 'VALUE') {
        // if (!value) {
        //   throw new Error('Value unit is required for `VALUE` button type');
        // }
        if (typeof value !== 'string') {
          throw new Error('Value unit must be a string');
        }
      } else if (value) {
        throw new Error(
          'Value unit should only be present for `VALUE` button type'
        );
      }
      return true;
    }),

  body('valueUnitDescription')
    .trim()
    .custom((value, { req }) => {
      if (req.body.buttonType === 'VALUE') {
        // if (!value) {
        //   throw new Error(
        //     'Value unit description is required for `VALUE` button type'
        //   );
        // }
        if (typeof value !== 'string') {
          throw new Error('Value unit description must be a string');
        }
      } else if (value) {
        throw new Error(
          'Value unit description should only be present for `VALUE` button type'
        );
      }
      return true;
    }),

  body('valueItemName')
    .trim()
    .custom((value, { req }) => {
      if (req.body.buttonType === 'VALUE') {
        if (value === undefined) {
          throw new Error(
            'Value item name is required for `VALUE` button type'
          );
        }
        if (typeof value !== 'string') {
          throw new Error('Value item name must be a string');
        }

        if (!value) {
          throw new Error(
            'Value item name is required for `VALUE` button type'
          );
        }
      } else if (value) {
        throw new Error(
          'Value item name should only be present for `VALUE` button type'
        );
      }
      return true;
    }),

  body('alarmTag', 'Alarm tag is required')
    .trim()
    .isBoolean()
    .withMessage('Alarm tag must be a boolean'),

  body('textNoteTag', 'Text note tag is required')
    .trim()
    .isBoolean()
    .withMessage('Text note tag must be a boolean'),

  body('locationTag', 'Location tag is required')
    .trim()
    .isBoolean()
    .withMessage('Location tag must be a boolean'),

  body('buttonSummeryCalculation', 'Button summary calculation is required')
    .trim()
    .notEmpty()
    .withMessage('Button summary calculation is required')
    .custom((value) => {
      if (!Array.isArray(value)) {
        throw new Error('Button summary calculation must be an array');
      }
      if (!value.every((item) => typeof item === 'string')) {
        throw new Error(
          'All items in button summary calculation must be strings'
        );
      }
      return true;
    }),

  body('buttonSequence', 'Button sequence is required')
    .trim()
    .notEmpty()
    .withMessage('Button sequence is required')
    .isInt()
    .withMessage('Button sequence must be an integer'),
];

exports.deleteButton = [
  ...headerValidator,
  query('buttonId')
    .trim()
    .notEmpty()
    .withMessage('Button id is required')
    .isInt()
    .withMessage('Button id must be an integer'),
];

exports.listButtons = [...headerValidator];

exports.getButtonIdeas = [...headerValidator];

exports.checkDeleted = [
  ...headerValidator,
  body('buttonIds')
    .isArray()
    .withMessage('buttonIds must be an array')
    .custom((value) => {
      if (!value.every(Number.isInteger)) {
        throw new Error('All button IDs must be integers');
      }
      return true;
    }),
  body('buttonUuids')
    .isArray()
    .withMessage('buttonUuids must be an array')
    .custom((value) => {
      if (!value.every((item) => typeof item === 'string')) {
        throw new Error('All button UUIDs must be strings');
      }
      return true;
    }),
];

exports.changeButtonSequence = [
  ...headerValidator,
  body('buttonSequence')
    .isArray()
    .withMessage('Button Sequence must be an array'),

  body('buttonSequence.*.buttonId')
    .exists()
    .withMessage('Each sequence item must have a buttonId')
    .isInt({ min: 0 })
    .withMessage('Each buttonId must be a non-negative integer'),

  body('buttonSequence.*.sequence')
    .exists()
    .withMessage('Each sequence item must have a sequence value')
    .isInt({ min: 0 })
    .withMessage('Each sequence value must be a non-negative integer'),
];
