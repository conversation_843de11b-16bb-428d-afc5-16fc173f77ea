const { header, body, query } = require('express-validator');

const { DEVICE_TYPE, USER_TYPE } = require('../../../Configs/constants');

exports.signUp = [
    body('firstName', 'Please Provide valid firstName!').trim().notEmpty(),
    body('lastName', 'Please Provide valid lastName!').optional({ nullable: true }),
    body('email', 'Please Provide valid email id!').optional({ nullable: true }),
    body('password', 'Please Provide password.').trim().notEmpty(),
    body('confirmPassword', 'Please Provide confirmPassword.').trim().notEmpty(),
    body('phoneNumber', 'Please Provide phoneNumber!').optional({ nullable: true }).trim(),
    body('countryCode', 'Please Provide countryCode!').optional({ nullable: true }).trim(),
    body('userType', 'Please Provide userType!').isIn(Object.values(USER_TYPE)).withMessage(`userType value should be in ${Object.values(USER_TYPE).join(", ")}`),
    body('deviceToken', 'Please Provide valid deviceToken!').trim().notEmpty(),
    body('deviceType', 'deviceType field is required!').isIn(Object.values(DEVICE_TYPE)).withMessage(`deviceType value should be in ${Object.values(DEVICE_TYPE).join(", ")}`),
    body('appVersion', 'appVersion field is required!').trim().notEmpty(),
];

exports.verifyOtp = [
    body('userId', 'userId field is required!').trim().notEmpty(),
    body('otp', 'otp field is required!').trim().notEmpty(),
    body('email', 'Please Provide valid email!').optional({ nullable: true }),
    body('phoneNumber', 'phoneNumber field is required!').optional({ nullable: true }).trim().notEmpty(),
    body('countryCode', 'countryCode field is required!').optional({ nullable: true }).trim().notEmpty(),
];

exports.resendOtp = [
    body('userId', 'userId field is required!').trim().notEmpty()
];

exports.signIn = [
    body('userType', 'Please Provide userType.').isIn(Object.values(USER_TYPE)).withMessage(`userType value should be in ${Object.values(USER_TYPE).join(", ")}`),
    body('emailOrPhoneNumber', 'Please Provide valid emailOrPhoneNumber.').if(body('userType').equals(USER_TYPE.NORMAL)).trim().notEmpty(),
    body('password', 'Please Provide password.').if(body('userType').equals(USER_TYPE.NORMAL)).trim().notEmpty(),

    body('firstName', 'Please Provide valid firstName!').if(body('userType').equals(USER_TYPE.GOOGLE)).trim().notEmpty(),
    body('lastName', 'Please Provide valid lastName!').if(body('userType').equals(USER_TYPE.GOOGLE)).trim().notEmpty(),
    body('email', 'Please Provide valid email!').if(body('userType').equals(USER_TYPE.GOOGLE)).trim().notEmpty().isEmail(),
    body('googleId', 'Please Provide valid googleId!').if(body('userType').equals(USER_TYPE.GOOGLE)).trim().notEmpty(),

    body('firstName', 'Please Provide valid firstName!').if(body('userType').equals(USER_TYPE.APPLE)).trim().notEmpty(),
    body('lastName', 'Please Provide valid lastName!').if(body('userType').equals(USER_TYPE.APPLE)).trim().notEmpty(),
    body('email', 'Please Provide valid email!').if(body('userType').equals(USER_TYPE.APPLE)).trim().notEmpty().isEmail(),
    body('appleId', 'Please Provide valid appleId!').if(body('userType').equals(USER_TYPE.APPLE)).trim().notEmpty(),

    body('appVersion', 'Please Provide valid appVersion!').trim().notEmpty(),
    body('deviceToken', 'Please Provide valid deviceToken!').trim().notEmpty(),
    body('deviceType', 'deviceType field is required!').isIn(Object.values(DEVICE_TYPE)).withMessage(`deviceType value should be in ${Object.values(DEVICE_TYPE).join(", ")}`)
];

exports.forgotPassword = [
    body('emailOrPhoneNumber', 'Please Provide valid emailOrPhoneNumber.').trim().notEmpty(),
];

exports.resetPassword = [
    body('userId', 'Please Provide user id.').trim().notEmpty(),
    body('otp', 'Please Provide otp.').trim().notEmpty(),
    body('newPassword', 'Please provide new password.').trim().notEmpty(),
    body('confirmPassword', 'Please provide confirmation password.').trim().notEmpty(),
];

exports.notification = [
    body('status', 'Please Provide notification status!').trim().notEmpty(),
]