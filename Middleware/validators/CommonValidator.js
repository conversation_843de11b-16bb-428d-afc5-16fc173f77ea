const { header, body, query } = require('express-validator')

const { ENTITY_STATUS } = require('../../Configs/constants')

exports.headerValidator = [
    header("authorization", "Please provide authorization.")
        .trim()
        .notEmpty()
]

exports.pageValidator = [
    query("page", "Please provide page.")
        .toInt()
        .isInt({ min: 1 })
        .withMessage("Please provide valid page."),
]

exports.perPageValidator = [
    query("perPage", "Please provide per page limit.")
        .toInt()
        .isInt({ min: 1 })
        .withMessage("Please provide valid per page limit."),
]

exports.pageBodyValidator = [
    body("page", "Please provide page.")
        .toInt()
        .isInt({ min: 1 })
        .withMessage("Please provide valid page."),
]

exports.perPageBodyValidator = [
    body("perPage", "Please provide per page limit.")
        .toInt()
        .isInt({ min: 1 })
        .withMessage("Please provide valid per page limit."),
]

exports.isActiveValidator = [
    body("isActive", "Please send isActive value either true or false.")
        .isBoolean()
]

exports.searchKeyValidator = [
	query("searchKey")
		.trim()
		.custom((value) => {
			if (value && value.toString().length < 3) {
				return false;
			}
			return true;
		})
		.withMessage("Minimum search value must be 3 characters long."),
];
