# Tracker Button

## Getting Started

If, you do not have VSCode or Node installed follow these steps and after the requirements are met follow the above mentioned steps.

- Download and setup [VSCode](https://code.visualstudio.com/download).

- Download and Install [NodeJs](https://nodejs.org/en/download/) version (20.18.3).

Download and extract the project in your project directory or,

Clone the project in your preferred folder from git CLI using

```bash
    cd your_project_folder
```

```bash
    git clone https://gitlab.openxcell.dev/a-team/tracker_button/newapis.git
```

```checkout
    git checkout --Name Required
```

If using Node Version Manager (nvm), you can install required node version (v20.18.3) with below command.

```bash
    nvm install
```

To switch to the required node version (v20.18.3) run below command.

```bash
    nvm use
```

Install dependencies

```bash
   npm install
```

## Prerequisites

Install the node_modules.

```
Hit the below command in Terminal:
-> npm install
```

### Installing

After getting the project in your local pull a branch that you'll be working on from git.

```
eg : git pull origin new-development
```

When cloned/downloaded the project, you will need to create a `.env` and change the following environment variables to run this project.

For this simply, copy and paste and rename `.env.new-development` to `.env` in your project folder.

> Note - Do not replace `.env.development`

Now, in your `.env` locate and change the

```
eg : Change DB_DATABASE=local
AND Change DB_HOST=localhost
```

Now Check migration in your database and if have any migration is down so run following command then run seeders if seeder data not add in database table

```bash
    npx sequelize-cli db:migrate:status
```

```bash
    npx sequelize-cli db:migrate
```

```bash
    npx sequelize-cli db:seed:all
```

Now run the project by entering the run command for node projects.

```
npm start
```

The project will be running fine if everything is perfect.

## Running the tests

After doing the changes in a local branch, commit the changes after checking and running it on local to avoid errors on staging. After ensuring everything is fine stage the changes.

You can stage the changes by :
-- git add <directory_name> (Stage all changes in directory for the next commit.)

After staging all the changes, commit the changes for push. The command to commit the changes:
-- git commit -am "commit message" (A power user shortcut command that combines the -a and -m options. This combination immediately creates a commit of all the staged changes and takes an inline commit message.)

After commit the changes, we are ready to push the changes.
-- git push origin new-development
(Since we already made sure the local main was up-to-date, this should result in a fast-forward merge, and git push should not complain about any of the non-fast-forward issues discussed above.)

## Built With

- [NodeJS](https://nodejs.org/en/docs/) - The web framework used
- (https://nodejs.org/en/docs/meta/topics/dependencies/) - Dependency Management

## Tech Stack

**Framework** - NodeJs, ReactJs

**Core Languages** - HTML5, CSS, Javascript

**DATABASE** - MySQL
