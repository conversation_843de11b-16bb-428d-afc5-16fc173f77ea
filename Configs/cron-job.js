/**
 * Cron Job Configuration and Utilities
 *
 * This file contains cron job patterns, timezone configurations,
 * and utility functions for managing scheduled tasks.
 */

const moment = require('moment');

/**
 * Cron Expression Format:
 * ┌───────────── minute (0 - 59)
 * │ ┌───────────── hour (0 - 23)
 * │ │ ┌───────────── day of month (1 - 31)
 * │ │ │ ┌───────────── month (1 - 12)
 * │ │ │ │ ┌───────────── day of week (0 - 6) (Sunday to Saturday)
 * │ │ │ │ │
 * │ │ │ │ │
 * * * * * *
 */

// Common cron patterns
const CRON_PATTERNS = {
  // Every minute
  EVERY_MINUTE: '* * * * *',

  // Every 5 minutes
  EVERY_5_MINUTES: '*/5 * * * *',

  // Every 15 minutes
  EVERY_15_MINUTES: '*/15 * * * *',

  // Every 30 minutes
  EVERY_30_MINUTES: '*/30 * * * *',

  // Every hour
  EVERY_HOUR: '0 * * * *',

  // Every 6 hours
  EVERY_6_HOURS: '0 */6 * * *',

  // Every 12 hours
  EVERY_12_HOURS: '0 */12 * * *',

  // Daily at midnight UTC
  DAILY_MIDNIGHT: '0 0 * * *',

  // Daily at 2 AM UTC (recommended for maintenance tasks)
  DAILY_2AM: '0 2 * * *',

  // Daily at 6 AM UTC
  DAILY_6AM: '0 6 * * *',

  // Weekly on Sunday at 2 AM UTC
  WEEKLY_SUNDAY_2AM: '0 2 * * 0',

  // Monthly on 1st day at 2 AM UTC
  MONTHLY_1ST_2AM: '0 2 1 * *',

  // Weekdays only at 9 AM UTC
  WEEKDAYS_9AM: '0 9 * * 1-5',

  // Weekend only at 10 AM UTC
  WEEKEND_10AM: '0 10 * * 0,6',
};

// Timezone configurations
const TIMEZONES = {
  UTC: 'UTC',
  EST: 'America/New_York',
  PST: 'America/Los_Angeles',
  GMT: 'Europe/London',
  IST: 'Asia/Kolkata',
  JST: 'Asia/Tokyo',
};

/**
 * Validates a cron expression
 * @param {string} cronExpression - The cron expression to validate
 * @returns {boolean} - True if valid, false otherwise
 */
const validateCronExpression = (cronExpression) => {
  const cronRegex =
    /^(\*|([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])|\*\/([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])) (\*|([0-9]|1[0-9]|2[0-3])|\*\/([0-9]|1[0-9]|2[0-3])) (\*|([1-9]|1[0-9]|2[0-9]|3[0-1])|\*\/([1-9]|1[0-9]|2[0-9]|3[0-1])) (\*|([1-9]|1[0-2])|\*\/([1-9]|1[0-2])) (\*|([0-6])|\*\/([0-6]))$/;
  return cronRegex.test(cronExpression);
};

/**
 * Gets the next execution time for a cron expression
 * @param {string} cronExpression - The cron expression
 * @param {string} timezone - The timezone (default: UTC)
 * @returns {string} - Next execution time in ISO format
 */
const getNextExecutionTime = (cronExpression, timezone = 'UTC') => {
  try {
    const cron = require('node-cron');
    // This is a simplified version - in production, use a proper cron parser
    return moment().tz(timezone).add(1, 'day').format('YYYY-MM-DD HH:mm:ss');
  } catch (error) {
    console.error('Error calculating next execution time:', error);
    return null;
  }
};

/**
 * Logs cron job execution with standardized format
 * @param {string} jobName - Name of the cron job
 * @param {string} status - Status (START, SUCCESS, ERROR, SKIP)
 * @param {string} message - Additional message
 * @param {Object} data - Additional data to log
 */
const logCronExecution = (jobName, status, message = '', data = null) => {
  const timestamp = moment().utc().format('YYYY-MM-DD HH:mm:ss');
  const logEntry = {
    timestamp,
    jobName,
    status,
    message,
    data,
  };

  const logMessage = `[CRON] [${timestamp}] [${jobName}] [${status}] ${message}`;

  if (status === 'ERROR') {
    console.error(logMessage, data ? JSON.stringify(data, null, 2) : '');
  } else {
    console.log(logMessage, data ? JSON.stringify(data, null, 2) : '');
  }

  return logEntry;
};

/**
 * Creates a safe cron job wrapper with error handling and logging
 * @param {string} jobName - Name of the job
 * @param {Function} jobFunction - The function to execute
 * @returns {Function} - Wrapped job function
 */
const createSafeCronJob = (jobName, jobFunction) => {
  return async () => {
    const startTime = moment();
    logCronExecution(jobName, 'START', 'Job execution started');

    try {
      const result = await jobFunction();
      const duration = moment().diff(startTime, 'seconds');
      logCronExecution(
        jobName,
        'SUCCESS',
        `Job completed in ${duration} seconds`,
        { duration, result }
      );
      return result;
    } catch (error) {
      const duration = moment().diff(startTime, 'seconds');
      logCronExecution(
        jobName,
        'ERROR',
        `Job failed after ${duration} seconds`,
        { duration, error: error.message, stack: error.stack }
      );
      throw error;
    }
  };
};

module.exports = {
  CRON_PATTERNS,
  TIMEZONES,
  validateCronExpression,
  getNextExecutionTime,
  logCronExecution,
  createSafeCronJob,
};
