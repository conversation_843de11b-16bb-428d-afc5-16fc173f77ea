global.chalk = require('chalk'); // FOR COLORED CONSOLES

// import('node-fetch')
//   .then((fetchModule) => {
//     global.fetch = fetchModule.default;
//   })
//   .catch((error) => {
//     console.error('Failed to import node-fetch:', error);
//   });

global.STATUS_CODES = require('./constants').STATUS_CODES;

global.sequelize = require('./database').sequelize;
global.Op = sequelize.Op;

global.moment = require('moment');
