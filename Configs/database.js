require('dotenv').config();
const fs = require('fs');
const Sequelize = require('sequelize');
const config = require('../Configs/config');

const DB_CREDENTIAL = {
  // database: process.env.DB_DATABASE,
  // username: process.env.DB_USERNAME,
  // password: process.env.DB_PASSWORD,
  // host: process.env.DB_HOST,
  // dialect: process.env.DB_CONNECTION,

  ...config[process.env.NODE_ENV],

  logging: process.env.DB_LOGGING === 'true' ? console.log : false,

  pool: {
    max: 500,
    min: 0,
    acquire: 60000,
    idle: 30000,
    evict: 1000,
  },
};

sequelize = new Sequelize(DB_CREDENTIAL);

sequelize
  .authenticate()
  .then(() => {
    console.log(
      `${DB_CREDENTIAL.database} database connected successfully :)\n`
    );
  })
  .catch((err) => {
    console.log('TCL: err', err);
    console.error('Unable to connect to the database :(\n');
  });

module.exports = {
  development: DB_CREDENTIAL,
  [process.env.NODE_ENV]: DB_CREDENTIAL,
  production: DB_CREDENTIAL,
  master: DB_CREDENTIAL,
  sequelize,
};
