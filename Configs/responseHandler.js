/* 
HOW TO USE THIS TO SEND RESPONSE

In your controller you can use

res.handler.*function*(data object*, message* , error*)
Ex : 
res.handler.success()
res.handler.success({userName : "<PERSON>"})
res.handler.success({userName : "<PERSON>"}, "User created")
res.handler.success(undefined, "User created")
res.handler.serverError(error object)

for message you can pass simple string
1. We have sent an email to your account
or for with values like
We have sent an email to %s,
{
    key : "TRANSLATION KEY",
    value : "value of %s"
}
*/

const { API } = require('../Configs/message');

class ResponseHandler {
  constructor(req, res) {
    this.req = req;
    this.res = res;
  }

  sender(code, message, data, error) {
    if (error) {
      if (error.code === 'LIMIT_FILE_SIZE')
        return this.res.status(STATUS_CODES.NOT_ALLOWED).json({
          message: error.message,
        });
      if (error?.original?.code === 'ER_DUP_ENTRY')
        return this.res.status(code).json({
          status: code,
          message: error.original.message,
        });

      // HANDLE LOGS AND OTHER STUFF
      console.log(
        '-----------------------------------------------------------------'
      );
      console.error(this.req.method + ' : ' + this.req.originalUrl);
      console.error('ERROR', error);
      console.error('Headers : ', JSON.stringify(this.req.headers));
      // console.error('Body : ', JSON.stringify(this.req.body));
      console.log(
        '-----------------------------------------------------------------'
      );

      return this.res.status(code).json({
        status: code,
        message: error.message,
        // error
      });
    }

    this.res.status(code).json({
      // message: (typeof message === 'string' ? this.res.__(message) : this.res.__(message.key, message.value)),
      message,
      data,
      status: code,
    });
  }

  /* 
        ARGUMENTS : Status code, message, data object,  error object
    */
  custom(...args) {
    this.sender(...args);
  }

  /* 
        ARGUMENTS : data o̥̥bject, message, error object
    */

  // 2XX SUCCESS
  success(message, data) {
    this.sender(STATUS_CODES.SUCCESS, message || API.SUCCESS, data);
  }

  created(message, data) {
    this.sender(STATUS_CODES.CREATED, message || API.CREATED, data);
  }

  // 4XX CLIENT ERROR
  badRequest(message, data) {
    this.sender(STATUS_CODES.BAD_REQUEST, message || API.BAD_REQUEST, data);
  }

  unauthorized(message, data, error) {
    this.sender(
      STATUS_CODES.UNAUTHORIZED,
      message || API.UNAUTHORIZED,
      data,
      error
    );
  }

  forbidden(message, data, error) {
    this.sender(STATUS_CODES.FORBIDDEN, message || API.FORBIDDEN, data, error);
  }

  notFound(message, data, error) {
    this.sender(STATUS_CODES.NOT_FOUND, message || API.NOT_FOUND, data, error);
  }

  conflict(message, data, error) {
    this.sender(STATUS_CODES.CONFLICT, message || API.CONFLICT, data, error);
  }

  preconditionFailed(message, data, error) {
    this.sender(
      STATUS_CODES.PRECONDITION_FAILED,
      message || API.PRECONDITION_FAILED,
      data,
      error
    );
  }

  validationError(message, error) {
    this.sender(
      STATUS_CODES.VALIDATION_ERROR,
      message || API.VALIDATION_ERROR,
      error
    );
  }

  // 5XX SERVER ERROR
  serverError(error) {
    console.log('error', error);
    this.sender(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR, undefined, error);
  }

  notAllowed(message, error) {
    this.sender(STATUS_CODES.NOT_ALLOWED, message || API.NOT_ALLOWED, error);
  }

  serviceUnavailable(message, error) {
    this.sender(
      STATUS_CODES.SERVICE_UNAVAILABLE,
      message || API.SERVICE_UNAVAILABLE,
      undefined,
      undefined,
      error
    );
  }
}

module.exports = ResponseHandler;
