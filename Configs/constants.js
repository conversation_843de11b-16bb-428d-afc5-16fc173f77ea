//GLOBAL STATUS
exports.STATUS_CODES = {
  // 1XX INFORMATIONAL
  CONTINUE: 100,
  SWITCHING_PROTOCOLS: 101,
  PROCESSING: 102,
  EARLY_HINTS: 103,

  // 2XX SUCCESS
  SUCCESS: 200,
  CREATED: 201,
  ACCEPTED: 202,
  NON_AUTHORITATIVE_INFORMATION: 203,
  NO_CONTENT: 204,
  RESET_CONTENT: 205,
  PARTIAL_CONTENT: 206,
  MULTI_STATUS: 207,
  ALREADY_REPORTED: 208,
  IM_USED: 226,

  // 3XX REDIRECTION
  MULTIPLE_CHOICES: 300,
  MOVED_PERMANENTLY: 301,
  FOUND: 302,
  SEE_OTHER: 303,
  NOT_MODIFIED: 304,
  USE_PROXY: 305,
  TEMPORARY_REDIRECT: 307,
  PERMANENT_REDIRECT: 308,

  // 4XX CLIENT ERROR
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  PAYMENT_REQUIRED: 402,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  NOT_ALLOWED: 405,
  NOT_ACCEPTABLE: 406,
  PROXY_AUTHENTICATION_REQUIRED: 407,
  REQUEST_TIMEOUT: 408,
  CONFLICT: 409,
  GONE: 410,
  LENGTH_REQUIRED: 411,
  PRECONDITION_FAILED: 412,
  PAYLOAD_TOO_LARGE: 413,
  URI_TOO_LONG: 414,
  UNSUPPORTED_MEDIA_TYPE: 415,
  RANGE_NOT_SATISFIABLE: 416,
  EXPECTATION_FAILED: 417,
  UNPROCESSABLE_ENTITY: 422,
  VALIDATION_ERROR: 422,
  NOT_VALID_DATA: 422,
  LOCKED: 423,
  FAILED_DEPENDENCY: 424,
  UNORDERED_COLLECTION: 425,
  UPGRADE_REQUIRED: 426,
  PRECONDITION_REQUIRED: 428,
  TOO_MANY_REQUESTS: 429,
  REQUEST_HEADER_FIELDS_TOO_LARGE: 431,
  UNAVAILABLE_FOR_LEGAL_REASONS: 451,

  // 5XX SERVER ERROR
  SERVER_ERROR: 500,
  NOT_IMPLEMENTED: 501,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
  HTTP_VERSION_NOT_SUPPORTED: 505,
  VARIANT_ALSO_NEGOTIATES: 506,
  INSUFFICIENT_STORAGE: 507,
  LOOP_DETECTED: 508,
  BANDWIDTH_LIMIT_EXCEEDED: 509,
  NOT_EXTENDED: 510,
  NETWORK_AUTHENTICATION_REQUIRED: 511,
};

exports.AWS_FOLDER = {
  USERS: {
    MAIN_DIRECTORY: '/users',
    PROFILE_IMAGE: '/profile-media',
    BACKUP: '/backup',
    ITEM_MEDIA: '/item-media',
  },
};

exports.USER_TYPE = {
  NORMAL: 'NORMAL',
  GOOGLE: 'GOOGLE',
  // APPLE: 'APPLE',
};

exports.SIGN_IN_TYPE = {
  EMAIL: 'EMAIL',
  PHONE: 'PHONE',
};

exports.DEVICE_TYPE = ['WEB', 'ANDROID', 'IOS'];
exports.SIGN_UP_TYPE = ['NORMAL', 'GOOGLE'];

exports.JWT = {
  APP_SECRET_KEY: process.env.APP_SECRET_KEY,
  ALGORITHM: process.env.ALGORITHM,
  EXPIRE_IN: process.env.EXPIRE_IN,
};

exports.SIGN_UP = {
  NORMAL: 'NORMAL',
  GOOGLE: 'GOOGLE',
};

exports.OTP_TYPES = {
  CREATE_USER: 'CREATE_USER',
  RESET_PASSWORD: 'RESET_PASSWORD',
};

// exports.SEND_OTP = {
//   EMAIL: 'EMAIL',
//   PHONE_NUMBER: 'PHONE_NUMBER',
// };

exports.SALT_ROUNDS = parseInt(process.env.ENCRYPTION_SALT_ROUNDS);

exports.FREE_BUTTON_COUNTS = parseInt(process.env.FREE_BUTTON_COUNTS);

exports.MAIL_DETAILS = {
  MAIL_SERVICE: process.env.EMAIL_SERVICE,
  MAIL_ID: process.env.EMAIL_ID,
  MAIL_PASSWORD: process.env.EMAIL_PASSWORD,
  MAIL_FROM: process.env.EMAIL_FROM,
  MAIL_HOST: process.env.MAIL_HOST,
  MAIL_PORT: process.env.MAIL_PORT,
  MAIL_METHOD: process.env.MAIL_METHOD,
  MAIL_SECURE: process.env.MAIL_SECURE,
  MAIL_SECURE_CONNECTION: process.env.MAIL_SECURE_CONNECTION,
  MAIL_REQUIRE_TLS: process.env.MAIL_REQUIRE_TLS,
  MAIL_DEBUG: process.env.MAIL_DEBUG,
};

exports.PLAY_CONSOLE_PACKAGE_NAME = process.env.PLAY_CONSOLE_PACKAGE_NAME;

exports.EMAIL_TEMPLATES_PATH = 'Views/Templates';

exports.EMAIL_TEMPLATE = {
  LOGO: process.env.HOST + `/Assets/Images/Tracker-Button.png`,
};

exports.BUTTON_TYPES = {
  COUNT: 'COUNT',
  DURATION: 'DURATION',
  VALUE: 'VALUE',
};

exports.BUTTON_TYPES_NUMBERS = {
  1: 'COUNT',
  2: 'DURATION',
  3: 'VALUE',
};

exports.BUTTON_TYPES_REVERSE = {
  COUNT: 1,
  DURATION: 2,
  VALUE: 3,
};

exports.BUTTON_SHAPES = {
  CIRCLE: 'CIRCLE',
  SQUARE: 'SQUARE',
};

exports.TAG_TYPES = {
  NOTE: 'NOTE',
  GPS: 'GPS',
};

exports.STATIC_PAGE_TYPES = {
  TERMS_CONDITIONS: 'TERMS_CONDITIONS',
  PRIVACY_POLICY: 'PRIVACY_POLICY',
};

// exports.STATIC_OTP = 1234;

exports.MEDIA_TYPE = ['IMAGE', 'AUDIO', 'VIDEO'];

exports.OPERATION_TYPES = {
  // Button Management
  ADD_BUTTON: 'ADD_BUTTON',
  EDIT_BUTTON: 'EDIT_BUTTON',
  DELETE_BUTTON: 'DELETE_BUTTON',

  // Button Item Management
  CREATE_COUNT_BUTTON_ITEM: 'CREATE_COUNT_BUTTON_ITEM',
  CREATE_DURATION_BUTTON_ITEM: 'CREATE_DURATION_BUTTON_ITEM',
  CREATE_VALUE_BUTTON_ITEM: 'CREATE_VALUE_BUTTON_ITEM',
  EDIT_COUNT_BUTTON_ITEM: 'EDIT_COUNT_BUTTON_ITEM',
  EDIT_DURATION_BUTTON_ITEM: 'EDIT_DURATION_BUTTON_ITEM',
  EDIT_VALUE_BUTTON_ITEM: 'EDIT_VALUE_BUTTON_ITEM',
  DELETE_COUNT_BUTTON_ITEM: 'DELETE_COUNT_BUTTON_ITEM',
  DELETE_DURATION_BUTTON_ITEM: 'DELETE_DURATION_BUTTON_ITEM',
  DELETE_VALUE_BUTTON_ITEM: 'DELETE_VALUE_BUTTON_ITEM',

  // Button Item Tag Management
  CREATE_ITEM_TAG: 'CREATE_ITEM_TAG',
  EDIT_ITEM_TAG: 'EDIT_ITEM_TAG',
  DELETE_ITEM_TAG: 'DELETE_ITEM_TAG',

  // Other Button Operations
  UPDATE_BUTTON_ALARM: 'UPDATE_BUTTON_ALARM',
  CHANGE_BUTTON_SEQUENCE: 'CHANGE_BUTTON_SEQUENCE',
};
