// const multer = require('multer');
// const getStream = require('into-stream');
// var moment = require('moment');
// var path = require('path');
// const AWS = require('aws-sdk');
// const fs = require('fs');
// const { AWS_DETAILS } = require("../Configs/constants");

// const credentials = {
// 	accessKeyId: AWS_DETAILS.AWS_ACCESS_KEY,
// 	secretAccessKey: AWS_DETAILS.AWS_SECRET_KEY,
// 	Bucket: AWS_DETAILS.AWS_BUCKET_NAME,
// 	region: AWS_DETAILS.AWS_REGION,
// };

// const { PATHS } = require('../Configs/constants');
// const { MEDIA } = PATHS;

// const tempPath = {
// 	export: path.join(__dirname, './../Assets/Media' + MEDIA.TEMP + MEDIA.EXPORT + '/'),
// };
// const s3 = new AWS.S3(credentials);
// let self;

// class AwsFileUpload {
// 	constructor() {
// 		self = this;
// 	}

// 	getBlobName(originalName) {
// 		let identifier = Math.floor(Math.random() * Math.floor(999));
// 		let newFilename = moment().unix().toString() + identifier;
// 		return newFilename + path.extname(originalName);
// 	}

// 	loadMulter() {
// 		return multer({ storage: multer.memoryStorage() });
// 	}

// 	async uploadToCloud(files, primary, secondary, extra = '', fileName) {
// 		if (!files) return;
// 		const arrayFiles = Array.isArray(files) ? files : [files];
// 		//const stream = getStream(files[0].buffer);

// 		return await Promise.all(
// 			arrayFiles.map((file) => {
// 				let cloudPath = primary + secondary;
// 				return new Promise((resolve, reject) => {
// 					let params = {
// 						Bucket: AWS_DETAILS.AWS_BUCKET_NAME,
// 						Body: stream,
// 						Key: (AWS_DETAILS.AWS_BUCKET_FOLDER || '') + cloudPath + '/' + fileName,
// 						ContentType: file.mimetype,
// 					};

// 					params.ACL = 'private';

// 					s3.upload(params, function (err, data) {
// 						if (err) reject(err);
// 						else {
// 							console.log(data);
// 							resolve(data);
// 						}
// 					});
// 				});
// 			})
// 		);
// 	}

// 	getUrl(pathName, fileName) {
// 		const bucket = AWS_DETAILS.AWS_BUCKET_NAME;
// 		const key = (AWS_DETAILS.AWS_BUCKET_FOLDER || '') + pathName + '/' + fileName;

// 		return `https://${bucket}.s3.${AWS_DETAILS.AWS_REGION}.amazonaws.com/${key}`;
// 	}

// 	async s3Delete(path) {

// 		return s3.deleteObject(
// 		  {
// 			Bucket: AWS_DETAILS.AWS_BUCKET_NAME,
// 			Key: path
// 		  },
// 		  function (err, data) {
// 			if (err) {
// 			  console.log("err", err);
// 			}
// 			console.log("Successfully deleted image on Amazon S3 ", data);
// 		  }
// 		);
// 	}

// 	async uploadZipToCloud(file, primary, secondary, extra) {
// 		let localFile = tempPath['export'] + secondary + '/' + extra + '/' + file;
// 		let cloudPath = primary + '/' + secondary + '/' + extra;
// 		return new Promise((resolve, reject) => {
// 			let params = {
// 				Bucket: process.env.AWS_BUCKET_NAME,
// 				Body: fs.createReadStream(localFile),
// 				Key: (process.env.AWS_BUCKET_FOLDER || '') + cloudPath + '/' + file,
// 				ContentType: path.extname(file),
// 			};

// 			s3.upload(params, function (err, data) {
// 				if (err) {
// 					reject(err);
// 				} else {
// 					console.log(data);
// 					resolve(data);
// 				}
// 			});
// 		});
// 	}

// 	async s3Upload(files, path) {

// 		return new Promise((resolve, reject) => {
// 			try {
// 				fs.readFile(files.path, (err, data) => {
// 					if (err) throw err;
// 					const params = {
// 						Bucket: AWS_DETAILS.AWS_BUCKET_NAME,
// 						Key: path,
// 						Body: files,
// 						ContentType: files.mimetype,
// 						Body: data,
// 						ACL: 'public-read'
// 					};

// 					s3.upload(params, function (err, rese) {
// 						if (err) {
// 							throw err;
// 						}
// 						console.log("functions3Upload -> rese.Location", rese.Location)
// 						resolve(rese.Location);

// 					});
// 				});
// 			} catch (e) {
// 				console.log("functions3Upload -> e", e)
// 				reject({ message: 'Could not upload image', err: e });
// 			}
// 		});
// 	}
// }

// module.exports = AwsFileUpload;
