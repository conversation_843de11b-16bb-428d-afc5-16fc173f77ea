require('dotenv').config();
require('newrelic');
const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const morgan = require('morgan');
const winstonLogger = require('./Logger/logger');

require('./Configs/globals');
// require('./Configs/cron-job');

const SHOULD_RUN_ON_HTTP = process.env.SHOULD_RUN_ON_HTTP;
const http = SHOULD_RUN_ON_HTTP == 'true' ? require('http') : require('https');

const app = express();

// Replace morgan's default logger with winston
const morganStream = {
  write: (message) => winstonLogger.info(message.trim()),
};
app.use(morgan('combined', { stream: morganStream }));
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));
app.use(express.json());

const server = http.createServer(app);

// ------* Language *------- //
const language = require('i18n');
language.configure({
  locales: ['en'],
  defaultLocale: 'en',
  autoReload: true,
  directory: __dirname + '/Locales',
  queryParameter: 'lang',
  objectNotation: true,
  syncFiles: true,
});
app.use(language.init); // MULTILINGUAL SETUP

// ------- Response Handler --------- //
app.use((req, res, next) => {
  const ResponseHandler = require('./Configs/responseHandler');
  res.handler = new ResponseHandler(req, res);
  next();
});

// ------- Routes --------- //
const appRoutes = require('./Routes');

// -------   GLOBAL ERROR HANDLER --------- //
app.use((err, req, res, next) => {
  if (res.headersSent) {
    return next(err);
  }
  if (!res.handler) {
    const ResponseHandler = require('./Configs/responseHandler');
    res.handler = new ResponseHandler(req, res);
    return res.handler.serverError(undefined, undefined, undefined, err);
  }
  return res.handler.serverError(undefined, undefined, undefined, err);
});

appRoutes(app);

require('./Crons');

// -------   GLOBAL UNCAUGHT EXCEPTION HANDLERS --------- //
process.on('uncaughtException', (error) => {
  console.error('=== UNCAUGHT EXCEPTION ===');
  console.error('Error:', error.message);
  console.error('Stack:', error.stack);
  console.error('Time:', new Date().toISOString());
  console.error('========================');

  // Log additional context if available
  if (error.code) console.error('Error Code:', error.code);
  if (error.moreInfo) console.error('More Info:', error.moreInfo);

  // Don't exit the process - keep the server running
  // process.exit(1); // Commented out to prevent crashes
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('=== UNHANDLED PROMISE REJECTION ===');
  console.error('Reason:', reason);
  console.error('Promise:', promise);
  console.error('Time:', new Date().toISOString());
  console.error('================================');

  // Don't exit the process - keep the server running
  // process.exit(1); // Commented out to prevent crashes
});

server.listen(process.env.PORT, async () => {
  console.log(`Server running at ${process.env.PORT}`);

  // Test Twilio credentials on startup
  try {
    const TwilioManager = require('./Helpers/Twilio.Managers');
    const twilioManager = new TwilioManager();
    console.log('[Startup] Twilio credentials validated successfully');
  } catch (error) {
    console.error(
      '[Startup] Twilio credential validation failed:',
      error.message
    );
    console.error('[Startup] SMS functionality may not work properly');
  }
});
