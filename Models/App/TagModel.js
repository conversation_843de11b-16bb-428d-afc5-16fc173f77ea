const { BUTTON_TYPES_REVERSE } = require('../../Configs/constants');
const { Tag: TagSchema } = require('../../Database/Schemas');

class TagModel {
  tagFormatInstance(tag) {
    const data = {
      tagId: tag.id || null,
      itemId: tag.item_id || null,
      buttonId: tag.button_id || null,
      tagUuid: tag.tag_uuid || null,
      buttonType: tag.button_type
        ? BUTTON_TYPES_REVERSE[tag.button_type]
        : null,
      tagType: tag.tag_type || null,
      tagTitle: tag.tag_title || null,
      tagType: tag.tag_type || null,
      tagValue: tag.tag_value || null,
      tagTimeStamp: tag.tag_time_stamp || null,
      createdAt: tag.created_at || null,
    };
    return data;
  }

  async findTag(filter, includeDeleted = false, options = {}) {
    if (includeDeleted) {
      return await TagSchema.unscoped().findOne({
        where: filter,
        raw: false,
        ...options,
      });
    } else {
      return await TagSchema.findOne({
        where: filter,
        raw: false,
        ...options,
      });
    }
  }

  async createTag(payload, options = {}) {
    return await TagSchema.create(payload, options);
  }

  async updateTag(filter, updateObj, options = {}) {
    return await TagSchema.update(updateObj, { where: filter, ...options });
  }

  async listTags(filter, includeDeleted = false, options = {}) {
    if (includeDeleted) {
      return await TagSchema.unscoped().findAll({
        where: filter,
        raw: false,
        ...options,
      });
    } else {
      return await TagSchema.findAll({
        where: filter,
        raw: false,
        ...options,
      });
    }
  }
}

module.exports = TagModel;
