const { BUTTON_TYPES_REVERSE } = require('../../Configs/constants');
const {
  Button: ButtonSchema,
  ButtonIdea: ButtonIdeaSchema,
  sequelize,
} = require('../../Database/Schemas');

class ButtonModel {
  buttonFormatInstance(button) {
    const data = {
      btnId: button.id || null,
      userId: button.user_id || null,
      buttonUuid: button.button_uuid || null,
      buttonName: button.button_name || null,
      buttonColor: button.button_color || null,
      buttonType: button.button_type
        ? BUTTON_TYPES_REVERSE[button.button_type]
        : null,
      buttonShape: button.button_shape || null,
      countInc: button.count_inc || null,
      valueUnit: button.value_unit || null,
      valueUnitDescription: button.value_unit_description || null,
      valueItemName:
        button.value_item_name === undefined || null
          ? null
          : button.value_item_name === true
          ? 'yes'
          : 'no',
      alarmTag: button.alarm_tag === undefined ? null : button.alarm_tag,
      textNoteTag:
        button.text_note_tag === undefined ? null : button.text_note_tag,
      locationTag:
        button.location_tag === undefined ? null : button.location_tag,
      buttonSummeryCalculation: button.button_summery_calculation
        ? JSON.parse(button.button_summery_calculation)
        : null,
      buttonSequence:
        button.button_sequence === undefined ? null : button.button_sequence,
      createdAt: button.created_at || null,
    };
    return data;
  }

  buttonIdeaFormatInstance(buttonIdea) {
    const data = {
      buttonideaId: buttonIdea.id || null,
      buttonIdea: buttonIdea.button_idea || null,
      buttonDescription: buttonIdea.button_description || null,
      buttonType: buttonIdea.button_type
        ? BUTTON_TYPES_REVERSE[buttonIdea.button_type]
        : null,
      // isActive: buttonIdea.is_active,
      // createdAt: buttonIdea.created_at || null,
    };

    return data;
  }

  async findButton(filter, includeDeleted = false, options = {}) {
    if (includeDeleted) {
      return await ButtonSchema.unscoped().findOne({
        where: filter,
        raw: false,
        ...options,
      });
    } else {
      return await ButtonSchema.findOne({
        where: filter,
        raw: false,
        ...options,
      });
    }
  }

  async createButton(payload, options = {}) {
    return await ButtonSchema.create(payload, options);
  }

  async createBulkButtons(payload = [], options = {}) {
    return await ButtonSchema.bulkCreate(payload, options);
  }

  async updateButton(filter, updateObj, options = {}) {
    return await ButtonSchema.update(updateObj, { where: filter, ...options });
  }

  async destroyButtons(filter, options = {}) {
    return await ButtonSchema.destroy({ where: filter, ...options });
  }

  async countButtons(filter, includeDeleted = false, options = {}) {
    if (includeDeleted) {
      return await ButtonSchema.unscoped().count({
        where: filter,
        ...options,
      });
    } else {
      return await ButtonSchema.count({
        where: filter,
        ...options,
      });
    }
  }

  async findButtons(filter, includeDeleted = false, options = {}) {
    if (includeDeleted) {
      return await ButtonSchema.unscoped().findAll({
        where: filter,
        raw: false,
        ...options,
      });
    } else {
      return await ButtonSchema.findAll({
        where: filter,
        raw: false,

        ...options,
      });
    }
  }

  async getButtonIdeas(options) {
    return await ButtonIdeaSchema.findAndCountAll({
      raw: false,
      ...options,
    });
  }

  async getTransaction() {
    return await sequelize.transaction();
  }
}

module.exports = ButtonModel;
