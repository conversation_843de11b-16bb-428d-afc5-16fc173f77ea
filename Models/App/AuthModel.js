const {
  User: UserSchema,
  UserToken: UserTokenSchema,
  Otp: OtpSchema,
} = require('../../Database/Schemas');
// const UserToken = require('../../Database/Schemas').user_tokens;
// const getUrl = new (require('../../Configs/awsUploader'))().getUrl;

class AuthModal {
  userFormatInstance(user) {
    const data = {
      userId: user.id,
      // firstName: user.first_name,
      // lastName: user.last_name || null,
      name: user.name || null,
      email: user.email || null,
      phoneNumber: user.phone_number || null,
      countryCode: user.country_code || null,
      //   profilePicture: user.profile_picture
      //     ? getUrl(AWS_DETAILS.AWS_USER_FOLDER + user.id, user.profile_picture)
      //     : null,
      userType: user.user_type,
      isVerified: !!user.is_verified,
      emailVerified: !!user.email_verified,
      phoneVerified: !!user.phone_verified,
      price: user.price || null,
      purchaseDate: user.purchase_date || null,
      isUpgraded: !!user.is_upgraded,
      // isNotify: !!user.is_notify,
      isActive: !!user.is_active,
      token: user.token || null,
      createdAt: user.created_at,
    };
    return data;
  }

  async findUser(filter) {
    return await UserSchema.findOne({ where: filter, raw: false });
  }

  async signUp(payload) {
    return await UserSchema.create(payload);
  }

  async userUpdate(updateObj, filter) {
    return await UserSchema.update(updateObj, { where: filter });
  }

  async saveOtp(payload) {
    return await OtpSchema.create(payload);
  }

  async findOtp(filter) {
    return await OtpSchema.findOne({ where: filter, raw: false });
  }

  async updateOtp(updateObj, filter) {
    return await OtpSchema.update(updateObj, { where: filter });
  }

  async deleteOtp(filter) {
    return await OtpSchema.destroy({ where: filter });
  }

  // async findUserUniqueNumber(filter) {
  //   return await UserUniqueNumberSchema.findOne({ where: filter, raw: false });
  // }

  // async createUserUniqueNumber(payload) {
  //   return await UserUniqueNumberSchema.create(payload);
  // }

  async findUserToken(filter) {
    return await UserTokenSchema.findOne({ where: filter, raw: false });
  }

  async createUserToken(payload) {
    return await UserTokenSchema.create(payload);
  }

  async updateUserToken(updateObj, filter) {
    return await UserTokenSchema.update(updateObj, { where: filter });
  }

  async checkToken(filter, options = {}) {
    return await UserTokenSchema.findAndCountAll({
      where: filter,
      raw: false,
      ...options,
    });
  }

  async deleteUserToken(filter) {
    return await UserTokenSchema.destroy({ where: filter });
  }

  async findAndCountAllUsers(
    filter,
    orderBy,
    order,
    attributes,
    limit,
    offset
  ) {
    return await UserSchema.findAndCountAll({
      where: filter,
      order: [[orderBy, order]],
      attributes: attributes,
      limit: limit,
      offset: offset,
    });
  }
}

module.exports = AuthModal;
