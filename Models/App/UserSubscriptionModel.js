const { UserSubscription: UserSubscriptionSchema } = require('../../Database/Schemas');

class UserSubscriptionModel {
  subscriptionFormatInstance(subscription) {
    return {
      id: subscription.id,
      userId: subscription.user_id,
      subscriptionId: subscription.subscription_id,
      transactionId: subscription.transaction_id,
      purchaseToken: subscription.purchase_token,
      purchaseAmount: subscription.purchase_amount,
      startDate: subscription.start_date,
      expirationDate: subscription.expiration_date,
    };
  }

  async findSubscriptionByField(filter, options = {}) {
    return await UserSubscriptionSchema.findOne({ where: filter, ...options });
  }

  async createSubscription(subscription) {
    return await UserSubscriptionSchema.create(subscription);
  }

  async updateSubscription(subscription, filter, options = {}) {
    return await UserSubscriptionSchema.update(subscription, {
      where: filter,
      ...options,
    });
  }

  async findAllSubscriptions(filter, options = {}) {
    return await UserSubscriptionSchema.findAll({ where: filter, ...options });
  }
}

module.exports = UserSubscriptionModel;