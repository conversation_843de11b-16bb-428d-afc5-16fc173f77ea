const { BUTTON_TYPES_REVERSE } = require('../../Configs/constants');
const { Item: ItemSchema } = require('../../Database/Schemas');

class ItemModel {
  itemFormatInstance(item) {
    const data = {
      itemId: item.id || null,
      buttonId: Number(item.button_id) || null,
      userId: item.user_id || null,
      itemUuid: item.item_uuid || null,
      buttonType: BUTTON_TYPES_REVERSE[item.button_type] || null,
      countIncrement: item.count_value || null,
      countTimeStamp: item.count_time_stamp || null,
      durationStartTimeStamp: item.duration_start_time_stamp || null,
      durationStopTimeStamp: item.duration_stop_time_stamp || null,
      durationTimeMs: item.duration_time_ms || null,
      itemName: item.item_name || null,
      itemValue: item.item_value || null,
      valueUnit: item.value_unit || null,
      valueTimeStamp: item.value_time_stamp || null,
      displayTime: item.display_time || null,
      displayDate: item.display_date || null,
      displayMonthYear: item.display_month_year || null,
      createdAt: item.created_at || null,
    };
    return data;
  }

  async findItem(filter, includeDeleted = false, options = {}) {
    if (includeDeleted) {
      return await ItemSchema.unscoped().findOne({
        where: filter,
        raw: false,
        ...options,
      });
    } else {
      return await ItemSchema.findOne({
        where: filter,
        raw: false,
        ...options,
      });
    }
  }

  async createItem(payload, options = {}) {
    return await ItemSchema.create(payload, options);
  }

  async updateItem(filter, updateObj, options = {}) {
    return await ItemSchema.update(updateObj, { where: filter, ...options });
  }

  //   async deleteButton(filter) {
  //     return await ButtonSchema.destroy({ where: filter });
  //   }

  async findItems(filter, includeDeleted = false, options = {}) {
    if (includeDeleted) {
      return await ItemSchema.unscoped().findAll({
        where: filter,
        raw: false,
        ...options,
      });
    } else {
      return await ItemSchema.findAll({
        where: filter,
        raw: false,
        ...options,
      });
    }
  }

  async countItems(filter, includeDeleted = false, options = {}) {
    if (includeDeleted) {
      return await ItemSchema.unscoped().count({
        where: filter,
        ...options,
      });
    }
    return await ItemSchema.count({
      where: filter,
      ...options,
    });
  }
}

module.exports = ItemModel;
