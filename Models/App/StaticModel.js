const { StaticPage: StaticPageSchema } = require('../../Database/Schemas');

class StaticModel {
  staticFormatInstance(staticContent) {
    return {
      staticId: staticContent.id || null,
      title: staticContent.title || null,
      description: staticContent.description || null,
      type: staticContent.type || null,
      // isActive: staticContent.is_active,
      // createdAt: staticContent.created_at || null,
    };
  }

  async fetchStatic(filter = {}, options = {}) {
    return await StaticPageSchema.findOne({
      where: filter,
      raw: false,
      ...options,
    });
  }
}

module.exports = StaticModel;
