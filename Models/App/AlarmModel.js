const { Alarm: AlarmSchema } = require('../../Database/Schemas');

class AlarmModel {
  alarmFormatInstance(alarm) {
    const data = {
      alarmId: alarm.id || null,
      buttonId: alarm.button_id || null,
      userId: alarm.user_id || null,
      buttonUuid: alarm.button_uuid || null,
      alarmTime: alarm.alarm_time || null,
      snoozeTime: alarm.snooze_time || null,
      repeatDaily:
        alarm.repeat_daily === true
          ? '1'
          : alarm.repeat_daily === false
          ? '0'
          : null,
      isRingAfter:
        alarm.is_ring_after === true
          ? '1'
          : alarm.is_ring_after === false
          ? '0'
          : null,
      isRingAfterAlways:
        alarm.is_ring_after_always === true
          ? '1'
          : alarm.is_ring_after_always === false
          ? '0'
          : null,
      ringAfterTimeStamp: alarm.ring_after_time_stamp || null,
      ringAfterTimeMs: alarm.ring_after_time_ms || null,
      isActive:
        alarm.is_active === true ? '1' : alarm.is_active === false ? '0' : null,
      createdAt: alarm.created_at || null,
    };

    return data;
  }

  async createAlarm(data, options = {}) {
    return await AlarmSchema.create(data, options);
  }

  async createBulkAlarms(payload, options = {}) {
    return await AlarmSchema.bulkCreate(payload, options);
  }

  async findAlarm(filter, options = {}) {
    return await AlarmSchema.findOne({ where: filter, ...options });
  }

  async updateAlarm(filter, updateObj, options = {}) {
    return await AlarmSchema.update(updateObj, { where: filter, ...options });
  }

  async countAlarms(filter, includeDeleted = false, options = {}) {
    if (includeDeleted) {
      return await AlarmSchema.unscoped().count({
        where: filter,
        ...options,
      });
    }
    return await AlarmSchema.count({
      where: filter,
      ...options,
    });
  }
}

module.exports = AlarmModel;
