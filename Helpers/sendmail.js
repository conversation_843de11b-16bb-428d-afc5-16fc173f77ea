require("dotenv").config();

const ejs = require("ejs");
const nodemailer = require("nodemailer");
var moment = require('moment');
const { MAIL_DETAILS } = require("../Configs/constants");

var sendmail = async function (
  res,
  email,
  subject,
  mailBody,
  attachments = ""
) {
  try {
    var transporter = nodemailer.createTransport({
      // service: MAIL_DETAILS.MAIL_SERVICE,
      host: MAIL_DETAILS.MAIL_HOST,
      port: MAIL_DETAILS.MAIL_PORT,
      secure: MAIL_DETAILS.MAIL_SECURE,
      secureConnection: MAIL_DETAILS.MAIL_SECURE_CONNECTION,
      requireTLS: MAIL_DETAILS.MAIL_REQUIRE_TLS,
      debug: MAIL_DETAILS.MAIL_DEBUG,
      auth: {
        user: MAIL_DETAILS.MAIL_FROM,
        pass: MAIL_DETAILS.MAIL_PASSWORD,
      },
    });

    let LOGO_URL = process.env.LOGO_URL ? process.env.LOGO_URL : "";
    let html_data = await ejs.renderFile(__dirname + "/email.ejs", {
      TITLE: subject,
      HTML_BODY: mailBody,
      LOGO_URL: LOGO_URL,
      APP_NAME: process.env.APP_NAME,
      APP_COLOR: process.env.APP_COLOR,
      YEAR: moment().format("YYYY")
    });

    let mailOption = {
      from: MAIL_DETAILS.MAIL_FROM,
      to: email,
      html: html_data,
      subject: subject,
    };

    if (attachments != "") {
      mailOption.attachments = attachments;
    }
    let mailResponse = await transporter.sendMail(mailOption);
    console.log("Mail send successfully");
    return mailResponse;
  } catch (error) {
    console.log(error);
    
    res.handler.serverError(error);
  }
};
exports.sendmail = sendmail;