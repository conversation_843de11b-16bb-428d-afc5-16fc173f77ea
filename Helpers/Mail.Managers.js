const nodemailer = require('nodemailer');
const ejs = require('ejs');
const path = require('path');
const fs = require('fs');
const constants = require('../Configs/constants');
const logoImage = path.join(__dirname, `../Assets/Images/Tracker-Button.png`)
const logoBase64 = fs.readFileSync(logoImage, { encoding: 'base64' });

const logoDataUri = `data:image/png;base64,${logoBase64}`;

module.exports = class {
  userVerificationOtp = (receiver, otp, data = '') => {
    return new Promise((resolve, reject) => {
      ejs.renderFile(
        // `${constants.EMAIL_TEMPLATES_PATH}otp-verification.ejs`,
        path.resolve(
          __dirname,
          `../${constants.EMAIL_TEMPLATES_PATH}/otp-verification.ejs`
        ),
        { otp, data, logo: logoImage /*constants.EMAIL_TEMPLATE.LOGO*/ },
        (err, htmlStr) => {
          if (err) {
            console.log('EJS rendering error:', err);
            reject(err);
            return;
          }

          const transporter = nodemailer.createTransport({
            service: process.env.EMAIL_SERVICE,
            auth: {
              user: process.env.EMAIL_ID,
              pass: process.env.EMAIL_PASSWORD,
            },
          });

          const mailOptions = {
            from: process.env.EMAIL_FROM,
            to: receiver,
            subject: `Email Verification`,
            html: htmlStr.replace('<%= logo %>', 'cid:trackerLogo'),
            attachments: [
              {
                filename: 'Tracker-Button.png',
                path: logoImage, // Path to the image
                cid: 'trackerLogo', // Content-ID
              },
            ],
          };
          transporter.sendMail(mailOptions, function (error, info) {
            if (error) {
              console.log('TCL -> error', error);
            }
          });
          resolve();
          return;
        }
      );
    });
  };

  resetPasswordOtp = (receiver, otp, data = '') => {
    console.log("logoDataUri", logoDataUri);
    
    return new Promise((resolve, reject) => {
      ejs.renderFile(
        // `${constants.EMAIL_TEMPLATES_PATH}reset-password-top.ejs`,
        path.resolve(
          __dirname,
          `../${constants.EMAIL_TEMPLATES_PATH}/reset-password-top.ejs`
        ),
        { otp, data, logo: logoDataUri /*constants.EMAIL_TEMPLATE.LOGO*/ },
        (err, htmlStr) => {
          if (err) {
            console.log('EJS rendering error:', err);
            reject(err);
            return;
          }

          const transporter = nodemailer.createTransport({
            // service: process.env.EMAIL_SERVICE,
            host: process.env.MAIL_HOST,
            auth: {
              user: process.env.EMAIL_ID,
              pass: process.env.EMAIL_PASSWORD,
            },
          });

          const mailOptions = {
            from: process.env.EMAIL_FROM,
            to: receiver,
            subject: `Reset Password Email Verification`,
            html: htmlStr.replace('<%= logo %>', 'cid:trackerLogo'),
            attachments: [
              {
                filename: 'Tracker-Button.png',
                path: logoImage, // Path to the image
                cid: 'trackerLogo', // Content-ID
              },
            ],
          };
          transporter.sendMail(mailOptions, function (error, info) {
            if (error) {
              console.log('TCL -> error', error);
            }
          });
          resolve();
          return;
        }
      );
    });
  };
};
