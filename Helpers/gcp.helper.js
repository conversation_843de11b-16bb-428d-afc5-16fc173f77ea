const path = require('path');
const { google } = require('googleapis');
const { PLAY_CONSOLE_PACKAGE_NAME } = require('../Configs/constants');

const keyFilePath = path.resolve(
  __dirname,
  '../Configs/trackerbuttons-4e8dd-2792688ce682.json'
);

async function authenticate() {
  const auth = new google.auth.GoogleAuth({
    keyFile: keyFilePath,
    scopes: ['https://www.googleapis.com/auth/androidpublisher'],
  });

  return google.androidpublisher({
    version: 'v3',
    auth,
  });
}

async function getSubscriptionDetails(token) {
  const packageName = PLAY_CONSOLE_PACKAGE_NAME;

  const androidPublisher = await authenticate();

  try {
    const response = await androidPublisher.purchases.subscriptionsv2.get({
      packageName,
      // subscriptionId,
      token,
    });

    return response.data;
  } catch (error) {
    console.error('Error fetching subscription status: ');

    console.dir(error.response?.data || error.message, { depth: 100 });
    // throw error;
  }
}

module.exports = {
  getSubscriptionDetails,
};
