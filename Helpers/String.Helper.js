module.exports = class StringHelper {
	static changeExt(fileName, newExt, extra = '') {
		let pos = fileName.includes('.') ? fileName.lastIndexOf('.') : fileName.length;
		let fileRoot = fileName.substr(0, pos);
		let output = `${fileRoot}${extra}.${newExt}`;
		return output;
	}

	static replaceInString(fullString, data) {
		data.forEach((changeObj) => {
			fullString = fullString.replace(changeObj.oldStringToGetReplaced, changeObj.newStringToReplace);
		});
		return fullString;
	}

	static getNameFromURL(url) {
		return url.substring(url.lastIndexOf('/') + 1);
	}
};
