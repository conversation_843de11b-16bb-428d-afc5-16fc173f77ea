
const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const phoneNumber = process.env.TWILIO_PHONE_NUMBER;

const client = require('twilio')(accountSid, authToken);

var smsHandler = async function (res, toNumber, textMessage) {
  let smsResponse = await client.messages.create({
    body: textMessage,
    from: phoneNumber,
    to: toNumber
  })
    .then(message => console.log(message.sid));
  console.log(`SMS send successfully on ${toNumber}`);
  return smsResponse;
};

exports.smsHandler = smsHandler;
