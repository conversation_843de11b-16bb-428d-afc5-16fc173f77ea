const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const { combineCountryCodeWithPhoneNumber } =
  new (require('../Helpers/common'))();

module.exports = class {
  // Validate Twilio credentials on initialization
  constructor() {
    this.validateCredentials();
  }

  validateCredentials() {
    if (!accountSid || !authToken || !process.env.TWILIO_PHONE_NUMBER) {
      console.error('[Twilio] Missing required Twilio credentials');
      throw new Error('Twilio credentials are not properly configured');
    }
  }

  sendSms = async (phone, message) => {
    try {
      // Validate inputs
      if (!phone || !message) {
        throw new Error('Phone number and message are required');
      }

      const client = require('twilio')(accountSid, authToken);

      // Properly await the promise and handle the response
      const messageResponse = await client.messages.create({
        body: message,
        from: process.env.TWILIO_PHONE_NUMBER,
        to: phone,
      });

      console.log(`[<PERSON><PERSON><PERSON>] SMS sent successfully to ${phone}, SID: ${messageResponse.sid}`);
      return { success: true, sid: messageResponse.sid };
    } catch (error) {
      // Enhanced error logging with specific Twilio error handling
      console.error(`[Twilio] Error sending SMS to ${phone}:`, {
        message: error.message,
        code: error.code,
        moreInfo: error.moreInfo,
        status: error.status,
        details: error.details
      });

      // Return error information instead of throwing
      return { 
        success: false, 
        error: {
          message: error.message,
          code: error.code,
          type: this.getErrorType(error)
        }
      };
    }
  };

  // Helper method to categorize Twilio errors
  getErrorType(error) {
    if (error.code === 20003) return 'AUTHENTICATION_ERROR';
    if (error.code === 21211) return 'INVALID_PHONE_NUMBER';
    if (error.code === 21608) return 'UNVERIFIED_PHONE_NUMBER';
    if (error.code === 21614) return 'INVALID_FROM_NUMBER';
    if (error.message && error.message.includes('Authenticate')) return 'AUTHENTICATION_ERROR';
    return 'UNKNOWN_ERROR';
  }

  userVerificationOtp = async (user = {}, otp = '') => {
    try {
      const phone = combineCountryCodeWithPhoneNumber(
        user?.country_code,
        user?.phone_number
      );
      const message = `Dear ${user?.name}, use this One Time Password ${otp} to verify your (Trackerbuttons) account. This OTP will be valid for the next 5 mins."`;
      
      if (phone && otp) {
        const result = await this.sendSms(phone, message);
        return result;
      } else {
        console.error('[Twilio] Missing phone number or OTP for user verification');
        return { success: false, error: { message: 'Missing phone number or OTP', type: 'VALIDATION_ERROR' } };
      }
    } catch (error) {
      console.error('[Twilio] Error in userVerificationOtp:', error);
      return { success: false, error: { message: error.message, type: 'SYSTEM_ERROR' } };
    }
  };

  resetPasswordOtp = async (user = {}, otp = '') => {
    try {
      const phone = combineCountryCodeWithPhoneNumber(
        user?.country_code,
        user?.phone_number
      );
      const message = `Dear ${user?.name}, use this One Time Password ${otp} to reset password verification for (Trackerbuttons) account. This OTP will be valid for the next 5 mins."`;
      
      if (phone && otp) {
        const result = await this.sendSms(phone, message);
        return result;
      } else {
        console.error('[Twilio] Missing phone number or OTP for password reset');
        return { success: false, error: { message: 'Missing phone number or OTP', type: 'VALIDATION_ERROR' } };
      }
    } catch (error) {
      console.error('[Twilio] Error in resetPasswordOtp:', error);
      return { success: false, error: { message: error.message, type: 'SYSTEM_ERROR' } };
    }
  };
};
