const fs = require('fs');
const crypto = require('crypto');
// const CryptoJS = require('crypto-js');
const moment = require('moment');
const _ = require('lodash');

module.exports = class {
  generateOTP() {
    return Math.floor(1000 + Math.random() * 7524);
  }

  createOTPValue() {
    let digits = '123456789',
      otpLength = 4,
      otp = '';

    for (let i = 0; i < otpLength; i++) {
      let index = Math.floor(Math.random() * digits.length);

      otp += digits[index];
    }

    return otp;
  }

  getName(userName) {
    const name = userName.split(' ');
    const firstName = name[0] || '';
    const lastName = name[1] || '';
    return {
      firstName,
      lastName,
    };
  }
  generatePassword() {
    let passwordGenerated = '';
    const str =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz0123456789@#$';
    for (let i = 1; i <= 12; i++) {
      const char = Math.floor(Math.random() * str.length + 1);
      passwordGenerated += str.charAt(char);
    }

    return passwordGenerated;
  }
  getFileName(fileName) {
    return (
      Date.now() +
      '_' +
      Math.floor(Math.random() * 999) +
      Math.floor(Math.random() * 79) * 157
    );
  }

  deleteFolderRecursive(path) {
    if (fs.existsSync(path)) {
      fs.rmSync(path, { recursive: true });
    }
  }

  encrypt(data) {
    try {
      // Get the encryption key from environment variables
      const encryptionKey = process.env.CRYPTO_ENCRYPTION_KEY;
      if (!encryptionKey) {
        throw new Error('Encryption key not found in environment variables.');
      }

      // Convert the key from Base64 to Buffer
      const key = Buffer.from(encryptionKey, 'base64');

      // Generate a random 16-byte IV (AES-256-CBC requires 16-byte IV)
      const iv = crypto.randomBytes(16);

      // Create a cipher using AES-256-CBC algorithm
      const cipher = crypto.createCipheriv(
        process.env.CRYPTO_ALGORITHM,
        key,
        iv
      );

      // Encrypt the data (data must be a string, so we convert it)
      let encrypted = cipher.update(data, 'utf-8', 'hex');
      encrypted += cipher.final('hex');

      // Combine the IV and encrypted data (to send/store together)
      const ivHex = iv.toString('hex'); // Convert IV to hex for storage/transport
      const result = ivHex + ':' + encrypted; // IV + Encrypted data

      return result;
    } catch (error) {
      console.error('Encryption failed:', error);
      throw error;
    }
  }

  decrypt(data) {
    try {
      // Get the encryption key from environment variables
      const encryptionKey = process.env.CRYPTO_ENCRYPTION_KEY;
      if (!encryptionKey) {
        throw new Error('Encryption key not found in environment variables.');
      }

      // Split the IV and encrypted data (they were joined by ':' during encryption)
      const [ivHex, encryptedData] = data.split(':');
      const iv = Buffer.from(ivHex, 'hex'); // Convert IV from hex back to Buffer

      // Convert Base64 key back to Buffer
      const key = Buffer.from(encryptionKey, 'base64');

      // Create the decipher using AES-256-CBC algorithm
      const decipher = crypto.createDecipheriv(
        process.env.CRYPTO_ALGORITHM,
        key,
        iv
      );

      // Decrypt the data
      let decrypted = decipher.update(encryptedData, 'hex', 'utf-8');
      decrypted += decipher.final('utf-8');

      return decrypted;
    } catch (error) {
      console.error('Decryption failed:', error);
      throw error;
    }
  }

  //   encrypt(data) {
  //     try {
  //       const encrypted = CryptoJS.AES.encrypt(
  //         data,
  //         process.env.CRYPTO_ENCRYPTION_KEY
  //       ).toString();
  //       const encoded = CryptoJS.enc.Base64.parse(encrypted).toString(
  //         CryptoJS.enc.Hex
  //       );
  //       return encoded;
  //     } catch (error) {
  //       return error;
  //     }
  //   }

  //   decrypt(data) {
  //     try {
  //       const decoded = CryptoJS.enc.Hex.parse(data).toString(
  //         CryptoJS.enc.Base64
  //       );
  //       const decrypted = CryptoJS.AES.decrypt(
  //         decoded,
  //         process.env.CRYPTO_ENCRYPTION_KEY
  //       ).toString(CryptoJS.enc.Utf8);
  //       return decrypted;
  //     } catch (error) {
  //       return error;
  //     }
  //   }

  requiredDate() {
    const dateElevenMonthsBack = moment().subtract(11, 'months');
    const daysToSubtract = parseInt(dateElevenMonthsBack.format('DD')) - 1;
    const selectedTimeFrame = moment(dateElevenMonthsBack)
      .subtract(daysToSubtract, 'days')
      .utc();

    return selectedTimeFrame;
  }

  arrayProcessed(requestedData, selectedTimeFrame) {
    const array = [];
    for (let index = 0; index <= 11; index++) {
      const month = moment(selectedTimeFrame)
        .add(index, 'month')
        .format('MMMM');
      const foundMonth = requestedData.find(
        (dataObj) => dataObj.MONTH === month
      );

      if (foundMonth) array.push({ MONTH: month, COUNT: foundMonth.COUNT });
      else array.push({ MONTH: month, COUNT: 0 });
    }
    return array;
  }

  makeId(length = 5) {
    var result = '';
    var characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var charactersLength = characters.length;
    for (var i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  }

  checkIsOTPInValidTime(otpCreatedAT) {
    const now = moment().utc();
    const end = moment(otpCreatedAT);
    const differenceInMinutes = moment.duration(now.diff(end)).asMinutes();
    if (differenceInMinutes < 30) return true;
    else return false;
  }

  combineCountryCodeWithPhoneNumber(country_code, phone_number) {
    if (!country_code && !phone_number) return '';
    const countryCodeWithPhoneNumber = `+${country_code} ${phone_number}`;
    return countryCodeWithPhoneNumber;
  }
};
