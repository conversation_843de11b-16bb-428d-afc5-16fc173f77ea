APP_NAME= Tracker Button

SHOULD_RUN_ON_HTTP=true

PORT=2577

#DATABASE CONFIG

NODE_ENV = 'development'
DB_DATABASE = tracker_button_new
DB_USERNAME = tracker_button_new
DB_PASSWORD = tLdgfs5mRvszbgf76djhdjs
DB_HOST = openxcell-development.c5uwiw99as4r.eu-west-1.rds.amazonaws.com
DB_CONNECTION = mysql
DB_LOGGING = false

#AWS CONFIG

AWS_SECRET_KEY = W4uXItlOk8+zjPPdfiUXw07pGPWsvWZbUOoOUgrf
AWS_ACCESS_KEY = ********************
AWS_BUCKET_NAME = openxcell-development-private
FILE_LIMITS = 20
AWS_BUCKET_FOLDER = tracker_button
AWS_REGION = ap-south-1
AWS_BUCKET_TYPE = private
AWS_SIGNED_URL_EXPIRE_TIME=600

#JWT CONFIG
APP_SECRET_KEY=MO/awB*sf5nvMd27
ALGORITHM=HS256
EXPIRE_IN=24h

CRYPTO_ENCRYPTION_KEY = "1ViU8BbiHeiCUNKnaZcMUolc805MHKHBSalXcTS6orc="
CRYPTO_ALGORITHM = aes-256-cbc

SECRET_KEY = 6Z1vf1UYufGCCyMrvE0o7zYTeqgPTuzw33Jg2N5618y8IK8rWFADj9P
ENCRYPTION_SALT_ROUNDS = 10

FREE_BUTTON_COUNTS=3

TWILIO_ACCOUNT_SID = **********************************
TWILIO_AUTH_TOKEN = 21a1304fc2ad6f8ee7d4d2e37fade2dd
TWILIO_PHONE_NUMBER = +***********

EMAIL_SERVICE=gmail
# EMAIL_ID ='<EMAIL>'
EMAIL_ID=<EMAIL>
# EMAIL_PASSWORD = 'iepxlkpdyqhzfgje'
# EMAIL_PASSWORD=P1anetearth
EMAIL_PASSWORD=P1@anetearth
# EMAIL_FROM="Tracker Button Team"
EMAIL_FROM=<EMAIL>
MAIL_HOST=smtpout.secureserver.net
MAIL_POR=465
MAIL_METHOD=SMTP
MAIL_SECURE=true
MAIL_SECURE_CONNECTION=false
MAIL_REQUIRE_TLS=true
MAIL_DEBUG=true

HOST=https://trackerbutton-newapi.apps.openxcell.dev

# Play console Configuration
PLAY_CONSOLE_PACKAGE_NAME = 'com.trackerbutton.dev'

# New Relic Configuration
NEW_RELIC_APP_NAME=trackerbuttons-api
NEW_RELIC_LICENSE_KEY=ce4751917a3b574ee62cba89ffc1bc02FFFFNRAL