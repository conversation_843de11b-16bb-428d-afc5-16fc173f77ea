{"name": "trackerbutton-apis", "version": "1.0.0", "description": "Tracker button apis", "main": "app.js", "scripts": {"start": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://gitlab.openxcell.dev/a-team/tracker_button/trackerbutton-apis/trackerbutton-apis.git"}, "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.0", "body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "ejs": "^3.1.10", "express": "^4.21.2", "express-validator": "^7.2.1", "googleapis": "^148.0.0", "i18n": "^0.15.1", "into-stream": "^8.0.1", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.12.0", "newrelic": "^12.22.0", "node-cron": "^4.0.5", "node-fetch": "^3.3.2", "nodemailer": "^6.10.0", "nodemon": "^3.1.9", "sequelize": "^6.37.5", "sequelize-cli": "^6.6.2", "twilio": "^5.4.4", "winston": "^3.17.0"}}