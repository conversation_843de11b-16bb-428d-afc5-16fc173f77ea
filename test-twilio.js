require('dotenv').config();

async function testTwilioCredentials() {
  console.log('=== Twilio Credential Test ===');
  
  // Check environment variables
  console.log('TWILIO_ACCOUNT_SID:', process.env.TWILIO_ACCOUNT_SID ? 'Set' : 'Missing');
  console.log('TWILIO_AUTH_TOKEN:', process.env.TWILIO_AUTH_TOKEN ? 'Set' : 'Missing');
  console.log('TWILIO_PHONE_NUMBER:', process.env.TWILIO_PHONE_NUMBER ? 'Set' : 'Missing');
  
  if (!process.env.TWILIO_ACCOUNT_SID || !process.env.TWILIO_AUTH_TOKEN || !process.env.TWILIO_PHONE_NUMBER) {
    console.error('❌ Missing required Twilio environment variables');
    return;
  }
  
  try {
    // Test Twilio client initialization
    const client = require('twilio')(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
    
    // Test account validation by fetching account info
    console.log('\n=== Testing Twilio Account Access ===');
    const account = await client.api.accounts(process.env.TWILIO_ACCOUNT_SID).fetch();
    console.log('✅ Account Status:', account.status);
    console.log('✅ Account SID:', account.sid);
    console.log('✅ Account Name:', account.friendlyName);
    
    // Test phone number validation
    console.log('\n=== Testing Phone Number ===');
    const phoneNumbers = await client.incomingPhoneNumbers.list();
    const twilioNumber = phoneNumbers.find(num => num.phoneNumber === process.env.TWILIO_PHONE_NUMBER);
    
    if (twilioNumber) {
      console.log('✅ Phone number is valid and active');
      console.log('✅ Phone Number:', twilioNumber.phoneNumber);
      console.log('✅ Capabilities:', twilioNumber.capabilities);
    } else {
      console.log('⚠️  Phone number not found in account or not verified');
      console.log('Available numbers:', phoneNumbers.map(num => num.phoneNumber));
    }
    
    console.log('\n✅ Twilio credentials are valid and working!');
    
  } catch (error) {
    console.error('\n❌ Twilio credential test failed:');
    console.error('Error Message:', error.message);
    console.error('Error Code:', error.code);
    console.error('Error Status:', error.status);
    console.error('More Info:', error.moreInfo);
    
    // Provide specific guidance based on error type
    if (error.code === 20003) {
      console.error('\n🔧 Fix: Check your TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN');
    } else if (error.message.includes('Authenticate')) {
      console.error('\n🔧 Fix: Your Twilio credentials appear to be invalid or expired');
    } else if (error.code === 21614) {
      console.error('\n🔧 Fix: Your TWILIO_PHONE_NUMBER is not valid or not verified');
    }
  }
}

// Test our enhanced Twilio Manager
async function testTwilioManager() {
  console.log('\n=== Testing Enhanced Twilio Manager ===');
  
  try {
    const TwilioManager = require('./Helpers/Twilio.Managers');
    const twilioManager = new TwilioManager();
    console.log('✅ Twilio Manager initialized successfully');
    
    // Note: Uncomment the line below to test actual SMS sending (will send real SMS)
    // const result = await twilioManager.sendSms('+**********', 'Test message from Twilio Manager');
    // console.log('SMS Test Result:', result);
    
  } catch (error) {
    console.error('❌ Twilio Manager test failed:', error.message);
  }
}

// Run tests
async function runTests() {
  await testTwilioCredentials();
  await testTwilioManager();
  console.log('\n=== Test Complete ===');
}

runTests().catch(console.error);
