const winston = require('winston');
const util = require('util');

const logger = winston.createLogger({
    level: 'info',
    format: winston.format.simple(),
    transports: [
        new winston.transports.Console(),
    ],
});

// Override default console methods to use winston
// Enhanced override: pretty-print objects with util.inspect
console.log = (...args) => {
  const formatted = args.map(arg =>
    typeof arg === 'object' && arg !== null
      ? util.inspect(arg, { depth: 100, colors: false })
      : String(arg)
  ).join(' ');
  logger.info(formatted);
};
console.error = (...args) => logger.error(args.map(String).join(' '));
console.warn = (...args) => logger.warn(args.map(String).join(' '));
console.dir = (obj, options = {}) => {
  const str = util.inspect(obj, { depth: options.depth ?? 100, colors: options.colors ?? false });
  logger.info('\n' + str);
};

module.exports = logger;