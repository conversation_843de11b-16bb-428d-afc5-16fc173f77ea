const { STATUS_CODES } = require('../../Configs/constants');
const { API } = require('../../Configs/message');

const AlarmModel = new (require('../../Models/App/AlarmModel'))();

class AlarmController {
  async updateAlarm(req, res) {
    try {
      const body = req.body;
      const userId = req.userId;

      const alarm = await AlarmModel.findAlarm({
        id: body.alarmId,
        button_id: body.buttonId,
        user_id: userId,
      });

      if (!alarm) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.ALARM_NOT_FOUND);
      }

      const updateObj = {
        alarm_time: body.alarmTime,
        snooze_time: body.snoozeTime,
        repeat_daily: body.repeatDaily,
        is_ring_after: body.isRingAfter,
        is_ring_after_always: body.isRingAfterAlways,
        ring_after_time_stamp: body.ringAfterTimeStamp,
        ring_after_time_ms: body.ringAfterTimeMs,
        is_active: body.isActive,
      };

      await AlarmModel.updateAlarm({ id: body.alarmId }, updateObj);

      const alarmData = await AlarmModel.findAlarm({ id: body.alarmId });

      const alarmDataFormatted = AlarmModel.alarmFormatInstance(alarmData);

      return res.handler.custom(
        STATUS_CODES.SUCCESS,
        API.ALARM_UPDATED,
        alarmDataFormatted
      );
    } catch (error) {
      return res.handler.serverError(error);
    }
  }
}

module.exports = AlarmController;
