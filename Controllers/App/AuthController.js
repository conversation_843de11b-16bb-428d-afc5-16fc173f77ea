const jwt = require('jsonwebtoken');
const Bcryptjs = require('bcryptjs'); /* For encryption and decryption */
const generatedSalt = Bcryptjs.genSaltSync(10);

const {
  STATUS_CODES,
  USER_TYPE,
  JWT,
  OTP_TYPES,
  BUTTON_TYPES,
  BUTTON_SHAPES,
  FREE_BUTTON_COUNTS,
} = require('../../Configs/constants');

const AuthModal = new (require('../../Models/App/AuthModel'))();
// const ButtonModel = new (require('../../Models/App/ButtonModel'))();
// const AlarmModel = new (require('../../Models/App/AlarmModel'))();
const { API } = require('../../Configs/message');
const methods = new (require('../../Helpers/common'))();
const mailSender = new (require('../../Helpers/Mail.Managers'))();
const smsSender = new (require('../../Helpers/Twilio.Managers'))();
const { Op } = require('sequelize');

class AuthController {
  async signUp(req, res) {
    try {
      const body = req.body;
      let userExists;
      if (body.email) {
        userExists = await AuthModal.findUser({ email: body.email });
        if (userExists) {
          return res.handler.custom(STATUS_CODES.CONFLICT, API.EMAIL_EXISTS);
        }
      }
      if (body.phoneNumber) {
        userExists = await AuthModal.findUser({
          phone_number: body.phoneNumber,
          country_code: body.countryCode,
        });
        if (userExists) {
          return res.handler.custom(
            STATUS_CODES.CONFLICT,
            API.PHONE_NUMBER_EXISTS
          );
        }
      }

      body.password = await Bcryptjs.hash(body.password, generatedSalt);
      const otp = methods.generateOTP();

      const payload = {};
      if (body.userType === USER_TYPE.NORMAL) {
        (payload['name'] = body?.name),
          (payload['email'] = body?.email),
          (payload['phone_number'] = body?.phoneNumber),
          (payload['country_code'] = body?.countryCode),
          (payload['password'] = body.password),
          (payload['user_type'] = body.userType),
          //   (payload['verify_otp'] = otp),
          (payload['is_verified'] = false),
          (payload['is_active'] = false),
          (payload['email_verified'] = false),
          (payload['phone_verified'] = false),
          //   (payload['is_notify'] = false),
          (payload['is_deleted'] = false),
          (payload['device_type'] = body.deviceType),
          (payload['device_token'] = body.deviceToken),
          (payload['app_version'] = body.appVersion);
      }

      if (body.phoneNumber) {
        const user = {
          name: body.name,
          country_code: body.countryCode,
          phone_number: body.phoneNumber,
        };
        const smsResult = await smsSender.userVerificationOtp(user, otp);
        if (!smsResult.success) {
          console.error(
            '[AuthController] SMS sending failed during signup:',
            smsResult.error
          );
          // Continue with signup process even if SMS fails - user can resend OTP later
        }
      } else if (body.email) {
        const receiver = body.email;
        await mailSender.userVerificationOtp(receiver, otp);
      }

      // create the user
      const user = await AuthModal.signUp(payload);

      const encryptedOtp = methods.encrypt(otp.toString());

      const userOtp = await AuthModal.saveOtp({
        user_id: user.id,
        otp_type: OTP_TYPES.CREATE_USER,
        otp: encryptedOtp,
      });

      // const data = {
      //   userId: user.id,
      //   name: user.name || null,
      //   email: user.email || null,
      //   phoneNumber: user.phone_number || null,
      //   countryCode: user.country_code || null,
      //   userType: user.user_type,
      //   isVerified: user.is_verified,
      //   emailVerified: user.email_verified,
      //   phoneVerified: user.phone_verified,
      // };

      const data = AuthModal.userFormatInstance(user);
      data.freeButtonCounts = FREE_BUTTON_COUNTS;

      // const defaultButtons = [
      //   {
      //     user_id: user.id,
      //     button_name: 'Drink water',
      //     button_color: '#8CFFFF',
      //     button_type: BUTTON_TYPES.COUNT,
      //     button_shape: BUTTON_SHAPES.CIRCLE,
      //     count_inc: '1',
      //     alarm_tag: true,
      //     text_note_tag: true,
      //     location_tag: true,
      //     button_summery_calculation: JSON.stringify([
      //       'Avg per day this week',
      //       'Total this week',
      //     ]),
      //     button_sequence: 0,
      //     is_deleted: false,
      //   },
      //   {
      //     user_id: user.id,
      //     button_name: 'Bills',
      //     button_color: '#FF3000',
      //     button_type: BUTTON_TYPES.VALUE,
      //     button_shape: BUTTON_SHAPES.CIRCLE,
      //     value_unit: '$',
      //     value_unit_description: 'Money spent',
      //     value_item_name: true,
      //     alarm_tag: true,
      //     text_note_tag: true,
      //     location_tag: true,
      //     button_summery_calculation: JSON.stringify([
      //       'Avg per day this week',
      //       'Total this week',
      //     ]),
      //     button_sequence: 1,
      //     is_deleted: false,
      //   },
      //   {
      //     user_id: user.id,
      //     button_name: 'Screen based activity',
      //     button_color: '#900001',
      //     button_type: BUTTON_TYPES.DURATION,
      //     button_shape: BUTTON_SHAPES.CIRCLE,
      //     alarm_tag: true,
      //     text_note_tag: true,
      //     location_tag: true,
      //     button_summery_calculation: JSON.stringify([
      //       'Avg per day this week',
      //       'Total this week',
      //     ]),
      //     button_sequence: 2,
      //     is_deleted: false,
      //   },
      // ];

      // const buttons = await ButtonModel.createBulkButtons(defaultButtons, {
      //   returning: true,
      // });

      // const defaultAlarms = buttons.map((button, index) => ({
      //   user_id: user.id,
      //   button_id: button.id,
      //   alarm_time: null,
      //   snooze_time: null,
      //   repeat_daily: false,
      //   is_ring_after: false,
      //   is_ring_after_always: false,
      //   ring_after_time_stamp: null,
      //   ring_after_time_ms: null,
      //   is_active: false,
      // }));

      // await AlarmModel.createBulkAlarms(defaultAlarms);

      return res.handler.success(API.REGISTER_SUCCESS, data);
    } catch (error) {
      console.error('Error in signUp:', error);
      return res.handler.serverError(error);
    }
  }

  async verifyOtp(req, res) {
    try {
      const body = req.body;
      let userData = await AuthModal.findUser({ id: body.userId });

      if (!userData) {
        return res.handler.custom(STATUS_CODES.CONFLICT, API.USER_NOT_FOUND);
      }

      if (userData.is_verified) {
        return res.handler.custom(
          STATUS_CODES.CONFLICT,
          API.USER_ALREADY_VERIFIED
        );
      }

      const encryptedOtp = await AuthModal.findOtp({ user_id: body.userId });

      if (!encryptedOtp) {
        return res.handler.custom(STATUS_CODES.CONFLICT, API.INVALID_OTP);
      }

      if (encryptedOtp.expiration_time < new Date()) {
        await AuthModal.deleteOtp({ user_id: body.userId });
        return res.handler.custom(STATUS_CODES.CONFLICT, API.EXPIRED_OTP);
      }

      const otp = methods.decrypt(encryptedOtp.otp);

      if (otp !== body.otp) {
        return res.handler.custom(STATUS_CODES.CONFLICT, API.INVALID_OTP);
      }

      if (body.otpType === OTP_TYPES.CREATE_USER) {
        const updateObj = {
          // verify_otp: null,
          is_active: true,
          is_verified: true,
        };

        if (body.phoneNumber) {
          updateObj['phone_verified'] = true;
        } else if (body.email) {
          updateObj['email_verified'] = true;
        }

        await AuthModal.userUpdate(updateObj, { id: body.userId });

        userData = await AuthModal.findUser({ id: body.userId });

        const jwtToken = jwt.sign(
          {
            user_id: userData.id,
          },
          JWT.APP_SECRET_KEY,
          {
            algorithm: JWT.ALGORITHM,
            // expiresIn: JWT.EXPIRE_IN,
          }
        );

        await AuthModal.createUserToken({
          user_id: userData.id,
          token: jwtToken,
        });

        await AuthModal.deleteOtp({ user_id: body.userId });

        const data = AuthModal.userFormatInstance({
          ...userData.dataValues,
          token: jwtToken,
        });
        data.freeButtonCounts = FREE_BUTTON_COUNTS;

        return res.handler.success(API.USER_VERIFIED_SUCCESS, data);
      }

      if (!userData.email_verified || !userData.phone_verified) {
        const updateObj = {
          email_verified: true,
          phone_verified: true,
        };

        await AuthModal.userUpdate(updateObj, { id: body.userId });

        const user = await AuthModal.findUser({ id: body.userId });
        const data = AuthModal.userFormatInstance(user);

        await AuthModal.deleteOtp({ user_id: user.id });

        return res.handler.success(API.USER_VERIFIED_SUCCESS, data);
      }
    } catch (err) {
      return res.handler.serverError(err);
    }
  }

  async resendOtp(req, res) {
    try {
      const body = req.body;
      let userData = await AuthModal.findUser({ id: body.userId });

      if (!userData) {
        return res.handler.custom(STATUS_CODES.CONFLICT, API.USER_NOT_FOUND);
      }

      if (userData.is_verified) {
        return res.handler.custom(
          STATUS_CODES.CONFLICT,
          API.USER_ALREADY_VERIFIED
        );
      }

      const otp = methods.generateOTP();

      if (userData.phone_number) {
        const user = {
          name: userData.name,
          country_code: body.countryCode,
          phone_number: body.phoneNumber,
        };
        const smsResult = await smsSender.userVerificationOtp(user, otp);
        if (!smsResult.success) {
          console.error(
            '[AuthController] SMS sending failed during resendOtp:',
            smsResult.error
          );
          // Return error to user if SMS fails during resend
          return res.handler.serverError({
            message: 'Failed to send SMS. Please try again or contact support.',
            details: smsResult.error,
          });
        }
      } else {
        const receiver = body.email;
        await mailSender.userVerificationOtp(receiver, otp);
      }

      const encryptedOtp = methods.encrypt(otp.toString());

      const otpData = await AuthModal.findOtp({ user_id: body.userId });

      if (otpData) {
        await AuthModal.updateOtp(
          {
            otp: encryptedOtp,
            expiration_time: new Date(new Date().getTime() + 5 * 60 * 1000),
            created_at: new Date(),
          },
          { user_id: body.userId }
        );
      } else {
        await AuthModal.saveOtp({
          user_id: body.userId,
          otp_type: OTP_TYPES.CREATE_USER,
          otp: encryptedOtp,
        });
      }

      return res.handler.success(API.RESEND_VERIFICATION_SUCCESS, null);
    } catch (err) {
      return res.handler.serverError(err);
    }
  }

  async signIn(req, res) {
    try {
      const body = req.body;
      let userData;
      let isNewUser = false;

      if (body.userType === USER_TYPE.NORMAL) {
        const filter = body.email
          ? { email: body.email }
          : body.phoneNumber
          ? { phone_number: body.phoneNumber, country_code: body.countryCode }
          : null;

        if (filter === null) {
          return res.handler.custom(
            STATUS_CODES.CONFLICT,
            API.USER_INVALID_CREDENTIALS
          );
        }

        userData = await AuthModal.findUser(filter);

        if (!userData) {
          return res.handler.custom(STATUS_CODES.CONFLICT, API.USER_NOT_FOUND);
        }

        if (userData.google_id) {
          return res.handler.custom(
            STATUS_CODES.CONFLICT,
            API.USER_INVALID_CREDENTIALS
          );
        }

        const isPasswordValid = Bcryptjs.compareSync(
          body.password,
          userData.password
        );

        if (!isPasswordValid) {
          return res.handler.custom(STATUS_CODES.CONFLICT, API.LOGIN_FAILED);
        }

        if (userData.is_deleted) {
          return res.handler.custom(STATUS_CODES.CONFLICT, API.USER_DELETED);
        }

        if (!userData.is_active) {
          return res.handler.custom(STATUS_CODES.CONFLICT, API.USER_INACTIVE);
        }

        if (!userData.is_verified) {
          const otp = methods.generateOTP();

          if (body.phoneNumber) {
            const user = {
              name: userData.name,
              country_code: body.countryCode,
              phone_number: body.phoneNumber,
            };
            const smsResult = await smsSender.userVerificationOtp(user, otp);
            if (!smsResult.success) {
              console.error(
                '[AuthController] SMS sending failed during signIn:',
                smsResult.error
              );
              // Continue with signIn process even if SMS fails - user can resend OTP later
            }
          } else if (body.email) {
            const receiver = body.email;
            await mailSender.userVerificationOtp(receiver, otp);
          }

          const encryptedOtp = methods.encrypt(otp.toString());

          const otpData = await AuthModal.findOtp({ user_id: userData.id });

          if (otpData) {
            await AuthModal.updateOtp(
              {
                otp: encryptedOtp,
                expiration_time: new Date(new Date().getTime() + 5 * 60 * 1000),
                created_at: new Date(),
              },
              { user_id: userData.id }
            );
          } else {
            await AuthModal.saveOtp({
              user_id: userData.id,
              otp_type: OTP_TYPES.CREATE_USER,
              otp: encryptedOtp,
            });
          }

          const data = AuthModal.userFormatInstance(userData);

          return res.handler.success(API.USER_UNVERIFIED, data);
        }
      } else if (body.userType === USER_TYPE.GOOGLE) {
        userData = await AuthModal.findUser({
          [Op.or]: { email: body.email, google_id: body.googleId },
        });

        if (userData) {
          if (!userData.google_id || body.googleId != userData.google_id) {
            return res.handler.notFound(API.USER_NORMAL_EXISTS);
          }

          if (body.email != userData.email) {
            return res.handler.notFound(API.USER_INVALID_CREDENTIALS);
          }

          if (!userData.is_active) {
            return res.handler.custom(STATUS_CODES.CONFLICT, API.USER_INACTIVE);
          }

          if (body.deviceToken != userData.device_token) {
            await AuthModal.userUpdate(
              { device_token: body.deviceToken },
              { id: userData.id }
            );
          }
        }

        if (userData) {
          if (body.googleId != userData.google_id) {
            return res.handler.custom(
              STATUS_CODES.CONFLICT,
              API.USER_INVALID_CREDENTIALS
            );
          }

          if (!userData.is_active) {
            return res.handler.custom(STATUS_CODES.CONFLICT, API.USER_INACTIVE);
          }
        } else {
          userData = await AuthModal.signUp({
            name: body.name,
            email: body?.email,
            google_id: body?.googleId,
            user_type: body.userType,
            is_verified: true,
            is_active: true,
            email_verified: true,
            device_type: body.deviceType,
            device_token: body.deviceToken,
            app_version: body.appVersion,
          });

          isNewUser = true;

          // const defaultButtons = [
          //   {
          //     user_id: userData.id,
          //     button_name: 'Drink water',
          //     button_color: '#8CFFFF',
          //     button_type: BUTTON_TYPES.COUNT,
          //     button_shape: BUTTON_SHAPES.CIRCLE,
          //     count_inc: '1',
          //     alarm_tag: true,
          //     text_note_tag: true,
          //     location_tag: true,
          //     button_summery_calculation: JSON.stringify([
          //       'Avg per day this week',
          //       'Total this week',
          //     ]),
          //     button_sequence: 0,
          //     is_deleted: false,
          //   },
          //   {
          //     user_id: userData.id,
          //     button_name: 'Bills',
          //     button_color: '#FF3000',
          //     button_type: BUTTON_TYPES.VALUE,
          //     button_shape: BUTTON_SHAPES.CIRCLE,
          //     value_unit: '$',
          //     value_unit_description: 'Money spent',
          //     value_item_name: true,
          //     alarm_tag: true,
          //     text_note_tag: true,
          //     location_tag: true,
          //     button_summery_calculation: JSON.stringify([
          //       'Avg per day this week',
          //       'Total this week',
          //     ]),
          //     button_sequence: 1,
          //     is_deleted: false,
          //   },
          //   {
          //     user_id: userData.id,
          //     button_name: 'Screen based activity',
          //     button_color: '#900001',
          //     button_type: BUTTON_TYPES.DURATION,
          //     button_shape: BUTTON_SHAPES.CIRCLE,
          //     alarm_tag: true,
          //     text_note_tag: true,
          //     location_tag: true,
          //     button_summery_calculation: JSON.stringify([
          //       'Avg per day this week',
          //       'Total this week',
          //     ]),
          //     button_sequence: 2,
          //     is_deleted: false,
          //   },
          // ];

          // const buttons = await ButtonModel.createBulkButtons(defaultButtons, {
          //   returning: true,
          // });

          // const defaultAlarms = buttons.map((button, index) => ({
          //   user_id: userData.id,
          //   button_id: button.id,
          //   alarm_time: null,
          //   snooze_time: null,
          //   repeat_daily: false,
          //   is_ring_after: false,
          //   is_ring_after_always: false,
          //   ring_after_time_stamp: null,
          //   ring_after_time_ms: null,
          //   is_active: false,
          // }));

          // await AlarmModel.createBulkAlarms(defaultAlarms);
        }
      }

      if (body.deviceToken != userData.device_token) {
        await AuthModal.userUpdate(
          { device_token: body.deviceToken },
          { id: userData.id }
        );
      }

      const jwtToken = jwt.sign(
        {
          user_id: userData.id,
        },
        JWT.APP_SECRET_KEY,
        {
          algorithm: JWT.ALGORITHM,
          // expiresIn: JWT.EXPIRE_IN,
        }
      );

      const findUserToken = await AuthModal.findUserToken({
        user_id: userData.id,
      });

      if (findUserToken) {
        await AuthModal.updateUserToken(
          { token: jwtToken },
          { user_id: userData.id }
        );
      } else {
        await AuthModal.createUserToken({
          user_id: userData.id,
          token: jwtToken,
        });
      }

      const user = await AuthModal.findUser({ id: userData.id });

      const data = AuthModal.userFormatInstance(user);
      data.token = jwtToken;
      data.freeButtonCounts = FREE_BUTTON_COUNTS;
      data.isNewUser = isNewUser;

      return res.handler.success(API.LOGIN_SUCCESS, data);
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async forgotPassword(req, res) {
    try {
      let body = req.body;

      const filter = body.email
        ? { email: body.email }
        : body.phoneNumber
        ? { phone_number: body.phoneNumber, country_code: body.countryCode }
        : null;

      const user = await AuthModal.findUser(filter);

      if (!user) {
        return res.handler.custom(STATUS_CODES.CONFLICT, API.USER_NOT_FOUND);
      }

      const otp = methods.generateOTP();

      if (body.email) {
        const receiver = user.email;

        await mailSender.resetPasswordOtp(receiver, otp);
      } else if (body.phoneNumber) {
        const smsResult = await smsSender.resetPasswordOtp(
          {
            name: user.name,
            country_code: user.country_code,
            phone_number: user.phone_number,
          },
          otp
        );
        if (!smsResult.success) {
          console.error(
            '[AuthController] SMS sending failed during forgotPassword:',
            smsResult.error
          );
          return res.handler.serverError({
            message: 'Failed to send SMS. Please try again or contact support.',
            details: smsResult.error,
          });
        }
      }

      const encryptedOtp = methods.encrypt(otp.toString());

      const otpData = await AuthModal.findOtp({ user_id: user.id });

      if (otpData) {
        await AuthModal.updateOtp(
          {
            otp: encryptedOtp,
            expiration_time: new Date(new Date().getTime() + 5 * 60 * 1000),
            created_at: new Date(),
          },
          { user_id: user.id }
        );
      } else {
        await AuthModal.saveOtp({
          user_id: user.id,
          otp_type: OTP_TYPES.RESET_PASSWORD,
          otp: encryptedOtp,
        });
      }

      return res.handler.success(API.FORGOT_PASSWORD_SUCCESS, null);
    } catch (err) {
      return res.handler.serverError(err);
    }
  }

  async resetPassword(req, res) {
    try {
      const body = req.body;
      // const filter = {
      //   id: body.userId,
      //   is_active: true,
      //   is_deleted: false,
      // };

      const filter = body.email
        ? { email: body.email }
        : body.phoneNumber
        ? { phone_number: body.phoneNumber, country_code: body.countryCode }
        : null;

      filter['is_active'] = true;
      filter['is_deleted'] = false;

      const user = await AuthModal.findUser(filter);

      if (!user) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.USER_NOT_FOUND);
      }

      if (!user.is_verified) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.USER_UNVERIFIED);
      }

      const encryptedOtp = await AuthModal.findOtp({
        user_id: user.id,
      });

      if (!encryptedOtp) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.INVALID_OTP);
      }

      if (encryptedOtp.expiration_time < new Date()) {
        await AuthModal.deleteOtp({ user_id: user.id });
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.EXPIRED_OTP);
      }

      const otp = methods.decrypt(encryptedOtp.otp);

      if (otp !== body.otp) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.INVALID_OTP);
      }

      if (body.newPassword != body.confirmPassword) {
        return res.handler.custom(
          STATUS_CODES.CONFLICT,
          API.PASSWORD_NOT_MATCH
        );
      }

      const newPassword = await Bcryptjs.hash(body.newPassword, generatedSalt);
      await AuthModal.userUpdate({ password: newPassword }, { id: user.id });
      await AuthModal.deleteOtp({ user_id: user.id });
      return res.handler.success(API.RESET_PASSWORD_SUCCESS, null);
    } catch (err) {
      return res.handler.serverError(err);
    }
  }

  async signOut(req, res) {
    try {
      const userId = req.userId;
      const userToken = req.headers.authorization;

      if (!userId) {
        return res.handler.custom(STATUS_CODES.CONFLICT, API.USER_NOT_FOUND);
      }

      if (!userToken) {
        return res.handler.custom(STATUS_CODES.CONFLICT, API.INVALID_TOKEN);
      }

      const user = await AuthModal.findUser({ id: userId });

      if (!user) {
        return res.handler.custom(STATUS_CODES.CONFLICT, API.USER_NOT_FOUND);
      }

      const token = await AuthModal.findUserToken({
        user_id: userId,
        token: userToken,
      });

      if (!token) {
        return res.handler.custom(STATUS_CODES.CONFLICT, API.INVALID_TOKEN);
      }

      await AuthModal.deleteUserToken({ user_id: userId });
      return res.handler.success(API.LOGOUT_SUCCESS, null);
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async checkStatus(req, res) {
    try {
      const body = req.body;

      const user = await AuthModal.findUser({
        email: body.email,
        google_id: body.googleId,
      });

      const userExist = user ? true : false;

      return res.handler.custom(STATUS_CODES.SUCCESS, API.SUCCESS, {
        userExist,
      });
    } catch (error) {
      return res.handler.serverError(error);
    }
  }
}

module.exports = AuthController;
