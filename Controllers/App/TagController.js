const { TAG_TYPES, STATUS_CODES } = require('../../Configs/constants');
const { API } = require('../../Configs/message');

const ButtonModel = new (require('../../Models/App/ButtonModel'))();
const ItemModel = new (require('../../Models/App/ItemModel'))();

const TagModel = new (require('../../Models/App/TagModel'))();

class TagController {
  async createTag(req, res) {
    try {
      const body = req.body;
      const userId = req.userId;

      const button = await ButtonModel.findButton({
        id: body.buttonId,
        user_id: userId,
      });

      if (!button) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.BUTTON_NOT_FOUND);
      }

      const item = await ItemModel.findItem({
        id: body.itemId,
        button_id: body.buttonId,
      });

      if (!item) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.ITEM_NOT_FOUND);
      }

      const payload = {
        item_id: body.itemId,
        button_id: body.buttonId,
        tag_uuid: body.tagUuid,
        tag_type: body.tagType,
        tag_title: body.tagTitle,
        tag_value: body.tagValue,
        tag_time_stamp: body.tagTimeStamp,
      };

      const tag = await TagModel.createTag(payload);

      const tagData = TagModel.tagFormatInstance({
        ...tag.dataValues,
        button_type: button.button_type,
      });

      return res.handler.custom(STATUS_CODES.SUCCESS, API.TAG_CREATED, tagData);
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async updateTag(req, res) {
    try {
      const body = req.body;
      const userId = req.userId;

      console.log('body', body);

      const button = await ButtonModel.findButton({
        id: body.buttonId,
        user_id: userId,
      });

      if (!button) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.BUTTON_NOT_FOUND);
      }

      const item = await ItemModel.findItem({
        id: body.itemId,
        button_id: body.buttonId,
      });

      if (!item) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.ITEM_NOT_FOUND);
      }

      const tag = await TagModel.findTag({
        id: body.tagId,
        button_id: body.buttonId,
        item_id: body.itemId,
      });

      if (!tag) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.TAG_NOT_FOUND);
      }

      if (tag.tag_type != body.tagType) {
        return res.handler.custom(
          STATUS_CODES.BAD_REQUEST,
          API.TAG_TYPE_DO_NOT_MATCH
        );
      }

      if (tag.tag_type === TAG_TYPES.GPS) {
        return res.handler.custom(
          STATUS_CODES.BAD_REQUEST,
          API.CANNOT_UPDATE_GPS_TAG
        );
      }

      const payload = {
        tag_title: body.tagTitle,
        tag_value: body.tagValue,
        tag_time_stamp: body.tagTimeStamp,
      };

      await TagModel.updateTag({ id: body.tagId }, payload);

      const tagData = await TagModel.findTag({
        id: body.tagId,
        button_id: body.buttonId,
        item_id: body.itemId,
      });

      const tagDataFormatted = TagModel.tagFormatInstance({
        ...tagData.dataValues,
        button_type: button.button_type,
      });

      return res.handler.custom(
        STATUS_CODES.SUCCESS,
        API.TAG_UPDATED,
        tagDataFormatted
      );
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async deleteTag(req, res) {
    try {
      const query = req.query;
      //   const userId = req.userId;

      const tag = await TagModel.findTag({
        id: query.tagId,
        button_id: query.buttonId,
        item_id: query.itemId,
      });

      if (!tag) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.TAG_NOT_FOUND);
      }

      await TagModel.updateTag({ id: query.tagId }, { is_deleted: true });

      return res.handler.custom(STATUS_CODES.SUCCESS, API.TAG_DELETED);
    } catch (error) {
      return res.handler.serverError(error);
    }
  }
}

module.exports = TagController;
