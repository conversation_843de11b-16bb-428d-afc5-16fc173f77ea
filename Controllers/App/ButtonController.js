const ButtonModel = new (require('../../Models/App/ButtonModel'))();
const ItemModel = new (require('../../Models/App/ItemModel'))();
const AuthModel = new (require('../../Models/App/AuthModel'))();
const { Item, Tag, Alarm } = require('../../Database/Schemas');
const { BUTTON_TYPES, STATUS_CODES } = require('../../Configs/constants');
const { API } = require('../../Configs/message');
const AlarmModel = new (require('../../Models/App/AlarmModel'))();
const TagModel = new (require('../../Models/App/TagModel'))();

class ButtonController {
  async createButton(req, res) {
    const body = req.body;
    try {
      const buttonType = body.buttonType;

      const userId = req.userId;

      const user = await AuthModel.findUser({ id: userId });

      if (!user) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.USER_NOT_FOUND);
      }

      const payload = {};
      payload.user_id = req.userId;
      payload.button_uuid = body.buttonUuid;
      payload.button_name = body.buttonName;
      payload.button_color = body.buttonColor;
      payload.button_type = body.buttonType;
      payload.button_shape = body.buttonShape;
      payload.button_summery_calculation = JSON.stringify(
        body.buttonSummeryCalculation
      );
      payload.button_sequence = body.buttonSequence;

      if (buttonType === BUTTON_TYPES.COUNT) {
        payload.count_inc = Number(body.countInc);
      } else if (buttonType === BUTTON_TYPES.VALUE) {
        payload.value_unit = body.valueUnit || null;
        payload.value_unit_description = body.valueUnitDescription || null;
        payload.value_item_name = body.valueItemName;
      }

      if (body.alarmTag !== undefined) payload.alarm_tag = body.alarmTag;
      if (body.textNoteTag !== undefined)
        payload.text_note_tag = body.textNoteTag;
      if (body.locationTag !== undefined)
        payload.location_tag = body.locationTag;

      const button = await ButtonModel.createButton(payload);

      if (!button) {
        return res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR);
      }

      const alarmPayload = {
        button_id: button.id,
        user_id: userId,
        button_uuid: button.button_uuid,
        alarm_time: null,
        snooze_time: null,
        repeat_daily: false,
        is_ring_after: false,
        is_ring_after_always: false,
        ring_after_time_stamp: null,
        ring_after_time_ms: null,
        is_active: false,
      };

      const alarm = await AlarmModel.createAlarm(alarmPayload);

      if (!alarm) {
        return res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR);
      }

      // const alarmData = AlarmModel.alarmFormatInstance(alarm);

      const buttonData = ButtonModel.buttonFormatInstance(button);

      buttonData.alarmId = alarm.id;

      return res.handler.custom(
        STATUS_CODES.SUCCESS,
        API.BUTTON_CREATED,
        buttonData
      );
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async fetchButton(req, res) {
    const query = req.query;
    try {
      const user = await AuthModel.findUser({ id: req.userId });

      if (!user) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.USER_NOT_FOUND);
      }

      const button = await ButtonModel.findButton({
        id: query.buttonId,
        user_id: req.userId,
      });

      if (!button) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.BUTTON_NOT_FOUND);
      }

      const buttonData = ButtonModel.buttonFormatInstance(button);

      // return res.handler.success(null, buttonData);
      return res.handler.custom(
        STATUS_CODES.SUCCESS,
        API.BUTTON_FETCHED,
        buttonData
      );
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async updateButton(req, res) {
    const body = req.body;
    const userId = req.userId;

    try {
      const user = await AuthModel.findUser({ id: userId });

      if (!user) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.USER_NOT_FOUND);
      }

      const button = await ButtonModel.findButton({
        id: body.buttonId,
        user_id: userId,
      });

      if (!button) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.BUTTON_NOT_FOUND);
      }

      const items = await ItemModel.countItems({
        button_id: body.buttonId,
      });

      if (items > 0 && button.button_type != body.buttonType) {
        return res.handler.custom(
          STATUS_CODES.BAD_REQUEST,
          API.BUTTON_HAVE_ITEM_HISTORY
        );
      }

      const payload = {};

      payload.button_name = body.buttonName;
      payload.button_color = body.buttonColor;
      payload.button_shape = body.buttonShape;
      payload.button_summery_calculation = JSON.stringify(
        body.buttonSummeryCalculation
      );
      payload.button_sequence = body.buttonSequence;

      // Check if button type is changed, then update the button type
      if (body.buttonType !== button.button_type) {
        payload.button_type = body.buttonType;

        if (body.buttonType === BUTTON_TYPES.COUNT) {
          payload.value_unit = null;
          payload.value_unit_description = null;
          payload.value_item_name = null;
        }

        if (body.buttonType === BUTTON_TYPES.VALUE) {
          payload.count_inc = null;
        }

        if (body.buttonType === BUTTON_TYPES.DURATION) {
          payload.count_inc = null;
          payload.value_unit = null;
          payload.value_unit_description = null;
          payload.value_item_name = null;
        }
      } else {
        if (button.button_type === BUTTON_TYPES.COUNT) {
          payload.count_inc = Number(body.countInc);
        } else if (button.button_type === BUTTON_TYPES.VALUE) {
          payload.value_unit = body.valueUnit || null;
          payload.value_unit_description = body.valueUnitDescription || null;
          payload.value_item_name = body.valueItemName;
        }
      }

      if (body.alarmTag !== undefined) payload.alarm_tag = body.alarmTag;
      if (body.textNoteTag !== undefined)
        payload.text_note_tag = body.textNoteTag;
      if (body.locationTag !== undefined)
        payload.location_tag = body.locationTag;

      await ButtonModel.updateButton(
        {
          id: body.buttonId,
          user_id: userId,
        },
        payload
      );

      const buttonData = await ButtonModel.findButton({
        id: body.buttonId,
        user_id: userId,
      });

      return res.handler.custom(
        STATUS_CODES.SUCCESS,
        API.BUTTON_UPDATED,
        ButtonModel.buttonFormatInstance(buttonData)
      );
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async deleteButton(req, res) {
    const query = req.query;

    try {
      const user = await AuthModel.findUser({ id: req.userId });

      if (!user) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.USER_NOT_FOUND);
      }

      const button = await ButtonModel.findButton({
        id: query.buttonId,
        user_id: req.userId,
      });

      if (!button) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.BUTTON_NOT_FOUND);
      }

      if (button.is_deleted) {
        return res.handler.custom(
          STATUS_CODES.CONFLICT,
          API.BUTTON_ALREADY_DELETED
        );
      }

      await ButtonModel.updateButton(
        {
          id: query.buttonId,
          user_id: req.userId,
        },
        { is_deleted: true }
      );

      return res.handler.custom(STATUS_CODES.SUCCESS, API.BUTTON_DELETED);
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async listButtons(req, res) {
    try {
      const user = await AuthModel.findUser({ id: req.userId });

      if (!user) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.USER_NOT_FOUND);
      }

      const buttons = await ButtonModel.findButtons(
        { user_id: req.userId },
        false,
        {
          order: [['button_sequence', 'ASC']],
          include: [
            {
              model: Item,
              as: 'items',
              required: false,
              include: [
                {
                  model: Tag,
                  as: 'tags',
                  required: false,
                },
              ],
            },
            {
              model: Alarm,
              as: 'alarm',
              required: false,
            },
          ],
        }
      );
      if (!buttons) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.BUTTON_NOT_FOUND);
      }

      // const buttonsData = buttons.map((button) => ({
      //   ...ButtonModel.buttonFormatInstance(button),
      //   items: button?.items?.length
      //     ? button.items.map(ItemModel.itemFormatInstance)
      //     : [],
      // }));

      const buttonsData = [];
      for (const button of buttons) {
        const formattedButton = ButtonModel.buttonFormatInstance(button);

        formattedButton.alarm = button.alarm
          ? AlarmModel.alarmFormatInstance(button.alarm)
          : {};

        const items = button.items?.length
          ? new Array(button.items.length)
          : [];

        if (items.length) {
          for (let i = 0; i < button.items.length; i++) {
            const formattedItem = ItemModel.itemFormatInstance({
              ...button.items[i].dataValues,
              button_type: button.button_type,
            });

            // Process tags if they exist
            if (button.items[i].tags?.length) {
              const formattedTags = button.items[i].tags.map((tag) =>
                TagModel.tagFormatInstance({
                  ...tag.dataValues,
                  button_type: button.button_type,
                })
              );
              formattedItem.tags = formattedTags;
            } else {
              formattedItem.tags = [];
            }

            items[i] = formattedItem;
          }
        }

        buttonsData.push({ ...formattedButton, items });
      }

      return res.handler.success(null, buttonsData);
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async getButtonIdeas(req, res) {
    try {
      const buttonIdeas = await ButtonModel.getButtonIdeas({
        where: { is_active: true },
      });

      if (!buttonIdeas.rows.length) {
        return res.handler.custom(
          STATUS_CODES.NOT_FOUND,
          API.BUTTON_IDEAS_EMPTY
        );
      }

      const formattedButtonIdeas = buttonIdeas.rows.map(
        ButtonModel.buttonIdeaFormatInstance
      );

      return res.handler.success(null, {
        count: buttonIdeas.count,
        buttonIdeas: formattedButtonIdeas,
      });
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async checkDeleted(req, res) {
    try {
      const { buttonIds, buttonUuids } = req.body;
      const userId = req.userId;

      const uuidResponse = [];
      let result = {};

      if (buttonIds.length === 0 && buttonUuids.length === 0) {
        return res.handler.success(null, {
          allDeleted: false,
          buttonUuids: [],
        });
      }

      if (buttonIds.length === 0) {
        result = { ...result, allDeleted: false };
      }

      if (buttonIds.length > 0) {
        // Find all buttons that exist and are not deleted
        const existingButtons = await ButtonModel.findButtons({
          id: buttonIds,
          user_id: userId,
        });

        // If no buttons exist or all are marked as deleted, return true
        if (!existingButtons || existingButtons.length === 0) {
          result = { ...result, allDeleted: true };
        }

        if (existingButtons.length > 0) {
          // If some buttons exist and are not deleted, return false
          result = { ...result, allDeleted: false };
        }
      }

      const existingButtonsUuid = await ButtonModel.findButtons({
        button_uuid: buttonUuids,
        user_id: userId,
      });

      if (!existingButtonsUuid || existingButtonsUuid.length === 0) {
        for (const uuid of buttonUuids)
          uuidResponse.push({
            buttonUuid: uuid,
            isExists: false,
          });

        result = { ...result, buttonUuids: uuidResponse };

        return res.handler.success(null, result);
      }

      if (buttonUuids.length > 0) {
        for (const uuid of buttonUuids) {
          const button = existingButtonsUuid.find(
            (button) => button.button_uuid === uuid
          );

          if (!button) {
            uuidResponse.push({
              buttonUuid: uuid,
              isExists: false,
            });
          } else {
            uuidResponse.push({
              buttonUuid: uuid,
              isExists: true,
            });
          }
        }

        result = { ...result, buttonUuids: uuidResponse };
      }

      // If any buttons exist and are not deleted, return false
      return res.handler.success(null, result);
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async changeButtonSequence(req, res) {
    const transaction = await ButtonModel.getTransaction();

    try {
      const { buttonSequence } = req.body;
      const userId = req.userId;

      const user = await AuthModel.findUser({ id: userId });
      if (!user) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.USER_NOT_FOUND);
      }

      const buttonIds = buttonSequence.map((item) => item.buttonId);

      const buttons = await ButtonModel.findButtons({
        user_id: userId,
        id: buttonIds,
      });

      if (buttons.length !== buttonIds.length) {
        await transaction.rollback();
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.BUTTON_NOT_FOUND);
      }

      const sequences = buttonSequence.map((b) => b.sequence);
      if (new Set(sequences).size !== sequences.length) {
        await transaction.rollback();
        return res.handler.custom(
          STATUS_CODES.BAD_REQUEST,
          API.DUPLICATE_SEQUENCE_FOUND
        );
      }

      const updatePromises = buttonSequence.map(async (item) => {
        return await ButtonModel.updateButton(
          { id: item.buttonId, user_id: userId },
          { button_sequence: item.sequence },
          { transaction }
        );
      });

      await Promise.all(updatePromises);

      await transaction.commit();

      return res.handler.custom(
        STATUS_CODES.SUCCESS,
        API.BUTTON_SEQUENCE_UPDATED
      );
    } catch (error) {
      await transaction.rollback();

      if (error.name === 'SequelizeValidationError') {
        return res.handler.custom(
          STATUS_CODES.BAD_REQUEST,
          API.INVALID_SEQUENCE_VALUES
        );
      }

      return res.handler.serverError(error);
    }
  }
}

module.exports = ButtonController;
