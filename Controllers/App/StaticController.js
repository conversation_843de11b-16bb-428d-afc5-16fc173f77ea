const { STATUS_CODES } = require('../../Configs/constants');
const { API } = require('../../Configs/message');

const StaticModel = new (require('../../Models/App/StaticModel'))();

class StaticController {
  async fetchStatic(req, res) {
    const query = req.query;
    try {
      const filter = {
        type: query.staticContentType,
      };

      const staticData = await StaticModel.fetchStatic(filter);

      if (!staticData) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.NOT_FOUND);
      }

      const formattedStaticData = StaticModel.staticFormatInstance(staticData);

      return res.handler.success(null, formattedStaticData);
    } catch (error) {
      return res.handler.serverError(error);
    }
  }
}

module.exports = StaticController;
