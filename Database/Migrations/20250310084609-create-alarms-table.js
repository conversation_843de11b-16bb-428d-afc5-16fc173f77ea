'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.createTable('alarms', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      button_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'buttons',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      alarm_time: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      snooze_time: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      repeat_daily: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      is_ring_after: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      is_ring_after_always: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      ring_after_time_stamp: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      ring_after_time_ms: {
        type: Sequelize.BIGINT,
        allowNull: true,
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.dropTable('alarms');
  },
};
