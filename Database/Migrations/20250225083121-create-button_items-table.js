'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('button_items', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      button_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'buttons',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      count_value: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      count_time_stamp: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      duration_start_time_stamp: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      duration_stop_time_stamp: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      duration_time_ms: {
        type: Sequelize.BIGINT.UNSIGNED,
        allowNull: true,
      },
      item_name: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      item_value: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      value_unit: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      value_time_stamp: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      display_time: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      display_date: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      display_month_year: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      is_deleted: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('button_items');
  },
};
