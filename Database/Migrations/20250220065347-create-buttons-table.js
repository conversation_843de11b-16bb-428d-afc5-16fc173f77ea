'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.createTable('buttons', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      button_name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      button_color: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      button_type: {
        type: Sequelize.ENUM('COUNT', 'DURATION', 'VALUE'),
        allowNull: false,
      },
      button_shape: {
        type: Sequelize.ENUM('CIRCLE', 'SQUARE'),
        allowNull: false,
      },
      count_inc: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      value_unit: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      value_unit_description: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      value_item_name: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      alarm_tag: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      text_note_tag: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      location_tag: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      button_summery_calculation: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      button_sequence: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      is_deleted: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.dropTable('buttons');
  },
};
