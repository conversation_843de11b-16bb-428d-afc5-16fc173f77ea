'use strict';

const { STATIC_PAGE_TYPES } = require('../../Configs/constants');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
    */

    await queryInterface.bulkInsert('static_pages', [{
      title: 'Terms and Conditions',
      description: '<p>Please read these terms and conditions carefully before using Our Service.</p>',
      type: STATIC_PAGE_TYPES.TERMS_CONDITIONS,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      title: 'Privacy Policy',
      description: '<p>Please read these privacy policy carefully before using Our Service.</p>',
      type: STATIC_PAGE_TYPES.PRIVACY_POLICY,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    ], {});
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
  }
};
