'use strict';

const { BUTTON_TYPES } = require('../../Configs/constants');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
    */

    await queryInterface.bulkInsert('button_ideas', [{
      button_idea: 'Medicine',
      button_description: 'Create a button with the name of the medicine. Click when you take medicine to log  when the medicine was taken.',
      button_type: BUTTON_TYPES.COUNT,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Driving',
      button_description: 'Log time you spent on driving.',
      button_type: BUTTON_TYPES.DURATION,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Petrol/Gas',
      button_description: 'Log amount spent on fuel/gas in litres/gallon.',
      button_type: BUTTON_TYPES.VALUE,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Study',
      button_description: 'Log hours spent every day for studies.Click the button when you starting studying and stop when you stop studying.',
      button_type: BUTTON_TYPES.DURATION,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Budget',
      button_description: 'Log all your spend.Trackerbuttons is the simplest app to track your spend.You may export your data for further analysis.',
      button_type: BUTTON_TYPES.VALUE,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Credit card',
      button_description: 'You may log the amount paid using credit card to help compare with credit card statement.',
      button_type: BUTTON_TYPES.VALUE,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Bills',
      button_description: 'Log the  amount paid for various bills.You can easily see average or total spend in a month.',
      button_type: BUTTON_TYPES.VALUE,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Electricity',
      button_description: 'Record amount paid on electricity bills to help track usage.',
      button_type: BUTTON_TYPES.VALUE,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Internet/Phone',
      button_description: 'You may record amount paid on internet/phone charges.',
      button_type: BUTTON_TYPES.VALUE,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'TV',
      button_description: 'You may record amount you pay for streaming platform subscriptions.',
      button_type: BUTTON_TYPES.VALUE,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Mobile',
      button_description: 'You may record amount paid on mobile  bills.',
      button_type: BUTTON_TYPES.VALUE,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Profit',
      button_description: 'If you sell anything - record profit made on each sale. Easy to see total profit over days/week/month etc.',
      button_type: BUTTON_TYPES.VALUE,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Exercise',
      button_description: 'Simply click to log when you exercised.Check the graph to see which days you exercised or missed.',
      button_type: BUTTON_TYPES.COUNT,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Drink Water',
      button_description: 'How many times drank water. This could be of type value and record the amount of water consumed.',
      button_type: BUTTON_TYPES.COUNT,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Diet',
      button_description: 'Count Calories.',
      button_type: BUTTON_TYPES.VALUE,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Jogging',
      button_description: `Record distance jogged at the end of your jog.You can set the 'Unit' as Miles or Kilometers in settings of the button.`,
      button_type: BUTTON_TYPES.VALUE,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Walking',
      button_description: 'At the end of your walk enter the distance you jog.',
      button_type: BUTTON_TYPES.VALUE,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Pushups',
      button_description: 'You may create buttons for individual exercise typ.',
      button_type: BUTTON_TYPES.COUNT,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Cholestrol',
      button_description: 'Record your cholestrol level. You can create butto.',
      button_type: BUTTON_TYPES.VALUE,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Sugar',
      button_description: 'Record the sugar value when you measure your sugar.',
      button_type: BUTTON_TYPES.VALUE,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Blood Pressure',
      button_description: 'You can create 2 buttons. One for BP Diastolic and.',
      button_type: BUTTON_TYPES.VALUE,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Fever Temperature',
      button_description: 'Maintain a fever log easily.Can share the log with.',
      button_type: BUTTON_TYPES.VALUE,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Sleeping',
      button_description: 'Record the number of hours slept.Start the button.',
      button_type: BUTTON_TYPES.DURATION,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Meditation',
      button_description: 'Track duration of your meditation.You may set an a.',
      button_type: BUTTON_TYPES.DURATION,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Car washing',
      button_description: 'Click on the button whenever you get your car wash.',
      button_type: BUTTON_TYPES.COUNT,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Car servicing',
      button_description: 'Click whenever you get your car serviced. You can add notes as well to the entry.',
      button_type: BUTTON_TYPES.COUNT,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Mobile game',
      button_description: 'Log the time duration kids spend playing games on mobile.',
      button_type: BUTTON_TYPES.DURATION,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Play time',
      button_description: 'Log the time duration kids play.Create a "Play Time" button.',
      button_type: BUTTON_TYPES.DURATION,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Coffee',
      button_description: 'Control by counting amount of coffee intake.',
      button_type: BUTTON_TYPES.COUNT,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Junk Food',
      button_description: 'Click whenever you have junk food.',
      button_type: BUTTON_TYPES.COUNT,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Whatsapp/Social media',
      button_description: 'Record time spent on Whatsapp/Social Media.',
      button_type: BUTTON_TYPES.DURATION,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Screen based activity',
      button_description: 'Log time spent on Screen based activity e.g. tv , computer , mobile etc.',
      button_type: BUTTON_TYPES.DURATION,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Number of hours worked',
      button_description: 'Count number of hours worked and perhaps overtime.',
      button_type: BUTTON_TYPES.DURATION,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Salary',
      button_description: 'Record salary received every month with the date and time.',
      button_type: BUTTON_TYPES.VALUE,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Sales figures',
      button_description: 'Record your sales numbers easily and get averages and totals.',
      button_type: BUTTON_TYPES.VALUE,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Nail biting',
      button_description: 'Count number of nail bitings in  a day.',
      button_type: BUTTON_TYPES.COUNT,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Smoking',
      button_description: 'Record number of cigarettes smoked when trying to quit. Aim for a downward trend.',
      button_type: BUTTON_TYPES.COUNT,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      button_idea: 'Periods tracker',
      button_description: `Track 'pink' days.`,
      button_type: BUTTON_TYPES.COUNT,
      is_active: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    ], {});

  },

  async down(queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
  }
};
