module.exports = (sequelize, DataTypes) => {
  const UserSubscriptionSchema = sequelize.define(
    'UserSubscription',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      subscription_id: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      transaction_id: {
        allowNull: false,
        type: DataTypes.STRING,
      },
      purchase_token: {
        allowNull: false,
        type: DataTypes.TEXT,
      },
      payment_amount: {
        allowNull: true,
        type: DataTypes.DECIMAL(10, 2),
      },
      start_date: {
        allowNull: false,
        type: DataTypes.DATE,
      },
      expiration_date: {
        allowNull: false,
        type: DataTypes.DATE,
      },
      deletion_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      data_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      tableName: 'user_subscriptions',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  UserSubscriptionSchema.associate = (models) => {
    UserSubscriptionSchema.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user',
    });
  };

  return UserSubscriptionSchema;
};
