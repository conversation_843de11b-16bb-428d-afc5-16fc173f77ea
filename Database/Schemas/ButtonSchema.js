const { BUTTON_TYPES, BUTTON_SHAPES } = require('../../Configs/constants');

module.exports = (sequelize, DataTypes) => {
  const ButtonSchema = sequelize.define(
    'Button',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      button_uuid: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: false,
      },
      button_name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      button_color: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      button_type: {
        type: DataTypes.ENUM([Object.values(BUTTON_TYPES)]),
        allowNull: false,
      },
      button_shape: {
        type: DataTypes.ENUM([Object.values(BUTTON_SHAPES)]),
        allowNull: false,
      },
      count_inc: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      value_unit: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      value_unit_description: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      value_item_name: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      alarm_tag: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      text_note_tag: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      location_tag: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      button_summery_calculation: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      button_sequence: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
    },
    {
      tableName: 'buttons',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      defaultScope: {
        where: {
          is_deleted: false,
        },
      },
      //   scopes: {
      //     withDeleted: {
      //       where: {
      //         is_deleted: true,
      //       },
      //     },
      //   },
    }
  );

  ButtonSchema.associate = (models) => {
    ButtonSchema.belongsTo(models.User, { foreignKey: 'user_id' });
    ButtonSchema.hasMany(models.Item, {
      targetKey: 'button_id',
      foreignKey: 'button_id',
      as: 'items',
    });
    ButtonSchema.hasOne(models.Alarm, {
      targetKey: 'button_id',
      foreignKey: 'button_id',
      as: 'alarm',
    });
  };

  return ButtonSchema;
};
