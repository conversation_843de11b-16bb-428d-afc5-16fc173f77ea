module.exports = (sequelize, DataTypes) => {
  const OtpSchema = sequelize.define(
    'Otp',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'users', // assuming you have a `users` table
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      otp_type: {
        type: DataTypes.ENUM('CREATE_USER', 'RESET_PASSWORD'),
        allowNull: false,
      },
      otp: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      expiration_time: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: Date.now() + 5 * 60 * 1000,
      },
    },
    {
      tableName: 'verify_otps', // Name of the table
      timestamps: true, // Use Sequelize's automatic createdAt and updatedAt fields
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  OtpSchema.associate = function (models) {
    OtpSchema.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user',
    });
  };

  OtpSchema.addHook('beforeCreate', (otp) => {
    const createdAt = otp.created_at.getTime();

    if (isNaN(createdAt)) {
      throw new Error('Invalid created_at value.');
    }

    otp.expiration_time = new Date(otp.created_at.getTime() + 5 * 60 * 1000);
  });

  return OtpSchema;
};
