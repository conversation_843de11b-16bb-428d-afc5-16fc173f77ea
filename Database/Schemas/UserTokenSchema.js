'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = (sequelize, DataTypes) => {
  const UserTokenSchema = sequelize.define(
    'UserToken',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onDelete: 'CASCADE',
      },
      token: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
    },
    {
      tableName: 'user_tokens',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  UserTokenSchema.associate = (models) => {
    UserTokenSchema.belongsTo(models.User, { foreignKey: 'user_id' });
  };

  return UserTokenSchema;
};
