const { BUTTON_TYPES } = require('../../Configs/constants');

module.exports = (sequelize, DataTypes) => {
  const ButtonIdeaSchema = sequelize.define(
    'ButtonIdea',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      button_idea: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      button_description: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      button_type: {
        type: DataTypes.ENUM(Object.values(BUTTON_TYPES)),
        allowNull: false,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
    },
    {
      tableName: 'button_ideas',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  return ButtonIdeaSchema;
};
