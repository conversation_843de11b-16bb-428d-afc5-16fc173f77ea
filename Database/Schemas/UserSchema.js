const { USER_TYPE, DEVICE_TYPE } = require('../../Configs/constants');

module.exports = (sequelize, DataTypes) => {
  const UserSchema = sequelize.define(
    'User',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      email: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      phone_number: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      country_code: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      google_id: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      password: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      user_type: {
        type: DataTypes.ENUM([
          USER_TYPE.NORMAL,
          USER_TYPE.GOOGLE,
          // USER_TYPE.APPLE,
        ]),
        allowNull: false,
      },
      is_verified: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      email_verified: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      phone_verified: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      device_type: {
        type: DataTypes.ENUM([DEVICE_TYPE]),
        allowNull: true,
      },
      device_token: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      app_version: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      price: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      purchase_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      is_upgraded: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      tableName: 'users',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      defaultScope: {
        where: {
          is_deleted: false,
        },
      },
      // scopes: {
      //   withDeleted: {
      //     where: {
      //       is_deleted: true,
      //     },
      //   },
      // },
    }
  );

  UserSchema.associate = (models) => {
    UserSchema.hasMany(models.Button, { foreignKey: 'user_id' });
  };
  return UserSchema;
};
