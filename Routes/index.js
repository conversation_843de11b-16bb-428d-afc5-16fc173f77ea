const express = require('express');
const app = express();

const appRoutes = require('./v1/App/index');

module.exports = (app) => {
  app.get('/', (req, res) => {
    console.log(
      'Welcome to ' + process.env.NODE_ENV + ' ' + process.env.APP_NAME
    );

    res
      .status(STATUS_CODES.SUCCESS)
      .send('Welcome to ' + process.env.NODE_ENV + ' ' + process.env.APP_NAME);
  });
  app.use('/app/v1', appRoutes);
};
