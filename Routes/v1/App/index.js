const express = require('express');
const router = express.Router();

const AuthRoutes = require('./AuthRoutes');
const UserRoutes = require('./UserRoutes');
const ButtonRoutes = require('./ButtonRoutes');
const StaticRoutes = require('./StaticRoutes');
const SyncRoutes = require('./SyncRoutes');
const WebhookRoutes = require('./WebhookRoutes');

router.use('/auth', AuthRoutes);
router.use('/user', UserRoutes);
router.use('/button', ButtonRoutes);
router.use('/static', StaticRoutes);
router.use('/sync', SyncRoutes);
router.use('/webhook', WebhookRoutes);

module.exports = router;
