const express = require('express');
const router = express.Router();

const Authentication =
  require('../../../Middleware/authentication').authentication;
const StaticValidator = require('../../../Middleware/validators/App/StaticValidator');
const StaticController =
  new (require('../../../Controllers/App/StaticController'))();

router
  .route('/')
  .get(
    StaticValidator.fetchStatic,
    Authentication,
    StaticController.fetchStatic
  );

module.exports = router;
