const express = require('express');
const router = express.Router();

const Authentication =
  require('../../../Middleware/authentication').authentication;
const ButtonValidator = require('../../../Middleware/validators/App/ButtonValidator');
const ItemValidator = require('../../../Middleware/validators/App/ItemValidator');
const TagValidator = require('../../../Middleware/validators/App/TagValidator');
const AlarmValidator = require('../../../Middleware/validators/App/AlarmValidator');
const AlarmController =
  new (require('../../../Controllers/App/AlarmController'))();
const TagController = new (require('../../../Controllers/App/TagController'))();
const ItemController =
  new (require('../../../Controllers/App/ItemController'))();
const ButtonController =
  new (require('../../../Controllers/App/ButtonController'))();

router
  .route('/')
  .post(
    ButtonValidator.createButton,
    Authentication,
    ButtonController.createButton
  );

router
  .route('/')
  .get(
    ButtonValidator.fetchButton,
    Authentication,
    ButtonController.fetchButton
  );

router
  .route('/')
  .put(
    ButtonValidator.updateButton,
    Authentication,
    ButtonController.updateButton
  );

router
  .route('/')
  .delete(
    ButtonValidator.deleteButton,
    Authentication,
    ButtonController.deleteButton
  );

router
  .route('/list')
  .get(
    ButtonValidator.listButtons,
    Authentication,
    ButtonController.listButtons
  );

router
  .route('/item')
  .post(ItemValidator.createItem, Authentication, ItemController.createItem);

router
  .route('/item')
  .put(ItemValidator.updateItem, Authentication, ItemController.updateItem);

router
  .route('/item')
  .delete(ItemValidator.deleteItem, Authentication, ItemController.deleteItem);

router
  .route('/item/list')
  .get(ItemValidator.listItems, Authentication, ItemController.listItems);

router
  .route('/item/tag')
  .post(TagValidator.createTag, Authentication, TagController.createTag);

router
  .route('/item/tag')
  .put(TagValidator.updateTag, Authentication, TagController.updateTag);

router
  .route('/item/tag')
  .delete(TagValidator.deleteTag, Authentication, TagController.deleteTag);

router
  .route('/alarm')
  .put(AlarmValidator.updateAlarm, Authentication, AlarmController.updateAlarm);

router
  .route('/sequence')
  .put(
    ButtonValidator.changeButtonSequence,
    Authentication,
    ButtonController.changeButtonSequence
  );

router
  .route('/ideas')
  .get(
    ButtonValidator.getButtonIdeas,
    Authentication,
    ButtonController.getButtonIdeas
  );

router
  .route('/check-deleted')
  .post(
    ButtonValidator.checkDeleted,
    Authentication,
    ButtonController.checkDeleted
  );

module.exports = router;
