const express = require('express');
const router = express.Router();

const AuthValidator = require('../../../Middleware/validators/App/AuthValidator');
const Authentication =
  require('../../../Middleware/authentication').authentication;
const AuthController =
  new (require('../../../Controllers/App/AuthController'))();

router
  .route('/signUp')
  .post(AuthValidator.signUp, Authentication, AuthController.signUp);

router
  .route('/verifyOtp')
  .post(AuthValidator.verifyOtp, Authentication, AuthController.verifyOtp);

router
  .route('/resendOtp')
  .post(AuthValidator.resendOtp, Authentication, AuthController.resendOtp);

router
  .route('/signIn')
  .post(AuthValidator.signIn, Authentication, AuthController.signIn);

router
  .route('/forgotPassword')
  .post(
    AuthValidator.forgotPassword,
    Authentication,
    AuthController.forgotPassword
  );

router
  .route('/resetPassword')
  .post(
    AuthValidator.resetPassword,
    Authentication,
    AuthController.resetPassword
  );

router
  .route('/signOut')
  .post(AuthValidator.signOut, Authentication, AuthController.signOut);

router
  .route('/checkUser')
  .post(AuthValidator.checkStatus, Authentication, AuthController.checkStatus);

module.exports = router;
