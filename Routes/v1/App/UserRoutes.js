const express = require('express');
const router = express.Router();

const UserValidator = require('../../../Middleware/validators/App/UserValidator');
const Authentication =
  require('../../../Middleware/authentication').authentication;
const UserController =
  new (require('../../../Controllers/App/UserController'))();

router
  .route('/')
  .get(
    UserValidator.getUserProfile,
    Authentication,
    UserController.getUserProfile
  );

router
  .route('/')
  .put(
    UserValidator.updateUserProfile,
    Authentication,
    UserController.updateUserProfile
  );

router
  .route('/')
  .delete(
    UserValidator.deleteUserProfile,
    Authentication,
    UserController.deleteUserProfile
  );

router
  .route('/changePassword')
  .post(
    UserValidator.changePassword,
    Authentication,
    UserController.changePassword
  );

router
  .route('/upgradeStatus')
  .post(
    UserValidator.upgradeStatus,
    Authentication,
    UserController.upgradeStatus
  );

router.route('/checkToken').post(UserValidator.checkTokenValidator, Authentication, UserController.checkToken);

router
  .route('/checkSub')
  .post(
    UserValidator.checkSubValidator,
    Authentication,
    UserController.checkSub
  );

module.exports = router;
