const express = require('express');
const router = express.Router();

const Authentication =
  require('../../../Middleware/authentication').authentication;
const SyncValidator = require('../../../Middleware/validators/App/SyncValidator');
// const {
//   withClientDisconnectHandling,
// } = require('../../../Middleware/timeoutMiddleware');

const SyncController =
  new (require('../../../Controllers/App/SyncController'))();

router
  .route('/')
  .post(SyncValidator.batchSyncValidator, Authentication, SyncController.batchSync);

module.exports = router;
